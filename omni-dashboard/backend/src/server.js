/**
 * OMNI-ALPHA VΩ∞∞ Trading System Dashboard API Server
 *
 * This server provides API endpoints and WebSocket connections for the OMNI-ALPHA VΩ∞∞ Trading System Dashboard.
 */

require('dotenv').config();
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const logger = require('../utils/logger');

// Import routes
const systemRoutes = require('./api/routes/system');
const tradesRoutes = require('./api/routes/trades');
const metricsRoutes = require('./api/routes/metrics');
const agentsRoutes = require('./api/routes/agents');
const assetsRoutes = require('./api/routes/assets');
const leaderboardRoutes = require('./api/routes/leaderboard');

// Import WebSocket handlers
const { setupWebSocketServer } = require('./websocket/socket-server');

// Create Express app
const app = express();

// Create HTTP server
const server = http.createServer(app);

// Create Socket.IO server
const io = socketIo(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:10001',
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Set up WebSocket server
setupWebSocketServer(io);

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:10001',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
app.use(express.json());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// Routes
app.use('/api/system', systemRoutes);
app.use('/api/trades', tradesRoutes);
app.use('/api/metrics', metricsRoutes);
app.use('/api/agents', agentsRoutes);
app.use('/api/assets', assetsRoutes);
app.use('/api/leaderboard', leaderboardRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`${err.status || 500} - ${err.message} - ${req.originalUrl} - ${req.method} - ${req.ip}`);
  res.status(err.status || 500).json({
    error: {
      message: err.message,
      status: err.status || 500
    }
  });
});

// Start server
const PORT = process.env.PORT || 10002;
const HOST = process.env.HOST || '0.0.0.0';
server.listen(PORT, HOST, () => {
  logger.info(`OMNI-ALPHA VΩ∞∞ Dashboard API Server running on ${HOST}:${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV}`);
  logger.info(`CORS Origin: ${process.env.CORS_ORIGIN || 'http://***********:10001'}`);
  logger.info(`WebSocket Server enabled`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});

module.exports = { app, server, io };
