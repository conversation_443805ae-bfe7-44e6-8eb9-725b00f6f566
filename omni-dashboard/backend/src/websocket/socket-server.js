/**
 * WebSocket Server for OMNI-ALPHA VΩ∞∞ Trading System Dashboard
 * 
 * This module sets up the WebSocket server for real-time updates to the dashboard.
 */

const { generateMockData } = require('../utils/mock-data-generator');
const logger = require('../utils/logger');

// Active connections
const connections = new Set();

// Setup WebSocket server
function setupWebSocketServer(io) {
  logger.info('Setting up WebSocket server');

  io.on('connection', (socket) => {
    logger.info(`Client connected: ${socket.id}`);
    connections.add(socket);

    // Send initial data
    sendInitialData(socket);

    // Handle subscription to specific data streams
    socket.on('subscribe', (channel) => {
      logger.info(`Client ${socket.id} subscribed to ${channel}`);
      socket.join(channel);
    });

    // Handle unsubscription from specific data streams
    socket.on('unsubscribe', (channel) => {
      logger.info(`Client ${socket.id} unsubscribed from ${channel}`);
      socket.leave(channel);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`Client disconnected: ${socket.id}`);
      connections.delete(socket);
    });
  });

  // Start sending periodic updates
  startPeriodicUpdates(io);

  return io;
}

// Send initial data to a client
function sendInitialData(socket) {
  const {
    systemStatus,
    activeTrades,
    tradeHistory,
    systemMetrics,
    agentStatus,
    assetInfo,
    leaderboard
  } = generateMockData();

  socket.emit('system:status', systemStatus);
  socket.emit('trades:active', activeTrades);
  socket.emit('trades:history', tradeHistory);
  socket.emit('metrics', systemMetrics);
  socket.emit('agents:status', agentStatus);
  socket.emit('assets:info', assetInfo);
  socket.emit('leaderboard', leaderboard);
}

// Start sending periodic updates to all clients
function startPeriodicUpdates(io) {
  // Update system status every 5 seconds
  setInterval(() => {
    const { systemStatus } = generateMockData();
    io.to('system:status').emit('system:status', systemStatus);
  }, 5000);

  // Update active trades every 2 seconds
  setInterval(() => {
    const { activeTrades } = generateMockData();
    io.to('trades:active').emit('trades:active', activeTrades);
  }, 2000);

  // Update metrics every 1 second
  setInterval(() => {
    const { systemMetrics } = generateMockData();
    io.to('metrics').emit('metrics', systemMetrics);
  }, 1000);

  // Update agent status every 3 seconds
  setInterval(() => {
    const { agentStatus } = generateMockData();
    io.to('agents:status').emit('agents:status', agentStatus);
  }, 3000);

  // Update asset info every 10 seconds
  setInterval(() => {
    const { assetInfo } = generateMockData();
    io.to('assets:info').emit('assets:info', assetInfo);
  }, 10000);

  // Update leaderboard every 30 seconds
  setInterval(() => {
    const { leaderboard } = generateMockData();
    io.to('leaderboard').emit('leaderboard', leaderboard);
  }, 30000);

  // Simulate trade events randomly
  setInterval(() => {
    if (Math.random() > 0.7) {
      const tradeEvent = {
        id: `trade-${Date.now()}`,
        type: Math.random() > 0.5 ? 'entry' : 'exit',
        symbol: ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'BNBUSDT'][Math.floor(Math.random() * 4)],
        price: 10000 + Math.random() * 40000,
        size: 0.001 + Math.random() * 0.01,
        direction: Math.random() > 0.5 ? 'long' : 'short',
        timestamp: new Date().toISOString(),
        agent: ['QuantumPredictor', 'ZeroLossEnforcer', 'HyperdimensionalPatternRecognizer'][Math.floor(Math.random() * 3)]
      };
      io.to('trades:events').emit('trades:event', tradeEvent);
    }
  }, 5000);
}

module.exports = {
  setupWebSocketServer
};
