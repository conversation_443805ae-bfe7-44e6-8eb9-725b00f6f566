//! OMNI-ALPHA VΩ∞∞ Trading System
//!
//! Main entry point for the OMNI-ALPHA VΩ∞∞ trading system.

use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::{sleep, Duration};
use tracing::{info, debug, warn, error, Level};
use tracing_subscriber::FmtSubscriber;
use anyhow::Result;

use omni::engine::message_bus::MessageBus;
use omni::engine::agent_trait::Agent;
use omni::exchange::bybit::adapter::BybitAdapter;
use omni::exchange::asset_discovery::AssetScanner;
use omni::agents::zero_loss_enforcer::{ZeroLossEnforcer, ZeroLossEnforcerConfig};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    let subscriber = FmtSubscriber::builder()
        .with_max_level(Level::INFO)
        .finish();
    tracing::subscriber::set_global_default(subscriber)?;
    
    info!("Starting OMNI-ALPHA VΩ∞∞ Trading System");
    info!("Capital Genesis: $12 USDT Origin Logic");
    info!("Recursive Intelligence Loop Activated");
    info!("Zero-Loss Enforcement Protocols Engaged");
    info!("Quantum Prediction System Online");
    info!("Multi-Agent Collaboration Network Established");
    info!("System Ready for Exponential Capital Growth");
    
    // Bybit Demo API Credentials
    let api_key = "lCMnwPKIzXASNWn6UE";
    let api_secret = "aXjs1SF9tmW3riHMktmjtyOyAT85puvrVstr";
    
    // Create Bybit adapter
    let bybit_adapter = Arc::new(BybitAdapter::new(api_key, api_secret, true)); // true = use demo API
    
    // Create message bus
    let message_bus = Arc::new(MessageBus::new());
    
    // Create Zero Loss Enforcer
    let zero_loss_config = ZeroLossEnforcerConfig::default();
    let mut zero_loss_enforcer = ZeroLossEnforcer::new(zero_loss_config, bybit_adapter.clone(), message_bus.clone());
    
    // Create Asset Scanner
    let asset_scanner = AssetScanner::new(
        bybit_adapter.clone(),
        20, // Max assets to analyze
        vec!["15".to_string(), "60".to_string()], // Timeframes to analyze
    );
    
    // Initialize Zero Loss Enforcer
    let agent_context = Arc::new(RwLock::new(omni::engine::agent_trait::AgentContext::default()));
    zero_loss_enforcer.initialize(agent_context).await?;
    
    // Start Zero Loss Enforcer
    zero_loss_enforcer.start().await?;
    
    // Check wallet balance
    info!("Checking wallet balance...");
    let balances = bybit_adapter.get_wallet_balance(Some("USDT")).await?;
    
    if let Some(usdt_balance) = balances.get("USDT") {
        info!("USDT Balance: ${:.2}", usdt_balance.available_balance);
        
        // Use $12 USDT for trading
        let trading_capital = 12.0_f64.min(usdt_balance.available_balance);
        info!("Using ${:.2} USDT for trading", trading_capital);
        
        // Main trading loop
        let mut iteration = 0;
        
        while iteration < 10 { // Run for 10 iterations
            info!("Trading iteration {}", iteration);
            
            // Scan for trading opportunities
            info!("Scanning for trading opportunities...");
            let opportunities = asset_scanner.scan_all_assets().await?;
            
            // Display top opportunities
            for (i, opportunity) in opportunities.iter().take(3).enumerate() {
                info!(
                    "Opportunity {}: {} - {} with score {:.2} at ${:.2}",
                    i + 1,
                    opportunity.symbol,
                    opportunity.action.to_uppercase(),
                    opportunity.score,
                    opportunity.price
                );
                info!("  Reason: {}", opportunity.reason);
            }
            
            // Execute top opportunity if score is high enough
            if let Some(top_opportunity) = opportunities.first() {
                if top_opportunity.score > 0.7 {
                    info!(
                        "Executing top opportunity: {} - {} at ${:.2}",
                        top_opportunity.symbol,
                        top_opportunity.action.to_uppercase(),
                        top_opportunity.price
                    );
                    
                    // Calculate position size (use 50% of capital for each trade)
                    let position_value = trading_capital * 0.5;
                    let quantity = position_value / top_opportunity.price;
                    
                    // Round quantity to appropriate precision
                    let quantity = (quantity * 1000000.0).round() / 1000000.0;
                    
                    info!("Position size: {} (value: ${:.2})", quantity, position_value);
                    
                    // Place order
                    let side = if top_opportunity.action == "buy" {
                        omni::exchange::OrderSide::Buy
                    } else {
                        omni::exchange::OrderSide::Sell
                    };
                    
                    let order = bybit_adapter.place_order(
                        "spot",
                        &top_opportunity.symbol,
                        side,
                        omni::exchange::OrderType::Market,
                        quantity,
                        None,
                        omni::exchange::TimeInForce::GoodTillCancel,
                        false,
                        false,
                        None,
                        None,
                    ).await;
                    
                    match order {
                        Ok(order) => {
                            info!("Order placed successfully! Order ID: {}", order.order_id);
                        },
                        Err(e) => {
                            error!("Failed to place order: {}", e);
                        }
                    }
                } else {
                    info!("No high-quality opportunities found. Waiting for better conditions.");
                }
            } else {
                info!("No trading opportunities found.");
            }
            
            // Update Zero Loss Enforcer
            zero_loss_enforcer.update().await?;
            
            // Sleep for 5 minutes
            info!("Sleeping for 5 minutes...");
            sleep(Duration::from_secs(300)).await;
            
            iteration += 1;
        }
    } else {
        error!("No USDT balance found");
    }
    
    // Stop Zero Loss Enforcer
    zero_loss_enforcer.stop().await?;
    
    info!("OMNI-ALPHA VΩ∞∞ Trading System stopped");
    
    Ok(())
}
