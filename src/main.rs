//! OMNI-ALPHA VΩ∞∞ Trading System
//!
//! Main entry point for the OMNI-ALPHA VΩ∞∞ trading system.

use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::{sleep, Duration};
use tracing::{info, debug, warn, error, Level};
use tracing_subscriber::FmtSubscriber;
use anyhow::Result;

use omni::trading_system::{TradingSystem, TradingSystemConfig, TradingMode, ExchangeConfig};
use omni::exchange::bybit::adapter::BybitAdapter;
use omni::exchange::asset_discovery::AssetScanner;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    let subscriber = FmtSubscriber::builder()
        .with_max_level(Level::INFO)
        .finish();
    tracing::subscriber::set_global_default(subscriber)?;
    
    info!("Starting OMNI-ALPHA VΩ∞∞ Trading System");
    info!("Capital Genesis: $12 USDT Origin Logic");
    info!("Recursive Intelligence Loop Activated");
    info!("Zero-Loss Enforcement Protocols Engaged");
    info!("Quantum Prediction System Online");
    info!("Multi-Agent Collaboration Network Established");
    info!("System Ready for Exponential Capital Growth");
    
    // Bybit Demo API Credentials
    let api_key = "lCMnwPKIzXASNWn6UE";
    let api_secret = "aXjs1SF9tmW3riHMktmjtyOyAT85puvrVstr";
    
    // Create Bybit adapter
    let bybit_adapter = Arc::new(BybitAdapter::new(api_key, api_secret, true)); // true = use demo API
    
    // Create Asset Scanner
    let asset_scanner = AssetScanner::new(
        bybit_adapter.clone(),
        50, // Max assets to analyze
        vec!["15".to_string(), "60".to_string(), "240".to_string()], // Timeframes to analyze
    );
    
    // Check wallet balance
    info!("Checking wallet balance...");
    let balances = bybit_adapter.get_wallet_balance(Some("USDT")).await?;
    
    if let Some(usdt_balance) = balances.get("USDT") {
        info!("USDT Balance: ${:.2}", usdt_balance.available_balance);
        
        // Create configuration
        let config = TradingSystemConfig {
            initial_capital: 12.0.min(usdt_balance.available_balance),
            mode: TradingMode::Live, // Use live mode with demo API
            assets: vec!["BTCUSDT".to_string(), "ETHUSDT".to_string(), "SOLUSDT".to_string(), "BNBUSDT".to_string(), "ADAUSDT".to_string()],
            timeframes: vec![15, 60, 240],
            max_concurrent_trades: 3,
            heartbeat_interval: 60,
            exchange: ExchangeConfig {
                name: "bybit".to_string(),
                api_key: api_key.to_string(),
                api_secret: api_secret.to_string(),
                testnet: false, // false = use demo API
                category: "spot".to_string(),
            },
        };
        
        // Create trading system
        let mut trading_system = TradingSystem::new(config);
        
        // Start trading system
        trading_system.start().await?;
        
        // Scan for initial trading opportunities
        info!("Scanning for initial trading opportunities...");
        let opportunities = asset_scanner.scan_all_assets().await?;
        
        // Display top opportunities
        for (i, opportunity) in opportunities.iter().take(5).enumerate() {
            info!(
                "Opportunity {}: {} - {} with score {:.2} at ${:.2}",
                i + 1,
                opportunity.symbol,
                opportunity.action.to_uppercase(),
                opportunity.score,
                opportunity.price
            );
            info!("  Reason: {}", opportunity.reason);
        }
        
        // Main loop
        let mut iteration = 0;
        while iteration < 100 { // Run for 100 iterations
            // Update trading system
            trading_system.update().await?;
            
            // Log current state
            if iteration % 10 == 0 {
                let state = trading_system.get_state();
                info!(
                    "Iteration: {}, Capital: ${:.2}, PnL: ${:.2} ({:.2}%), Active trades: {}, Completed trades: {}",
                    iteration,
                    state.current_capital,
                    state.current_pnl,
                    state.current_roi,
                    state.active_trades_count,
                    state.completed_trades_count
                );
            }
            
            // Scan for new trading opportunities every 10 iterations
            if iteration % 10 == 0 {
                info!("Scanning for new trading opportunities...");
                let opportunities = asset_scanner.scan_all_assets().await?;
                
                // Display top opportunities
                for (i, opportunity) in opportunities.iter().take(3).enumerate() {
                    info!(
                        "Opportunity {}: {} - {} with score {:.2} at ${:.2}",
                        i + 1,
                        opportunity.symbol,
                        opportunity.action.to_uppercase(),
                        opportunity.score,
                        opportunity.price
                    );
                }
            }
            
            // Sleep for the heartbeat interval
            sleep(Duration::from_secs(trading_system.get_config().heartbeat_interval)).await;
            
            iteration += 1;
        }
        
        // Stop trading system
        trading_system.stop().await?;
        
        info!("OMNI-ALPHA VΩ∞∞ Trading System stopped");
    } else {
        error!("No USDT balance found");
    }
    
    Ok(())
}
