{"name": "aethir-x", "version": "0.1.0", "description": "Exponential Sovereign Intelligence Substrate", "main": "index.js", "scripts": {"build": "./scripts/build.sh", "test": "./scripts/test.sh", "dev": "./scripts/dev.sh", "setup": "./scripts/setup.sh", "start": "node dist/index.js", "clean": "./scripts/clean.sh", "docs": "./scripts/docs.sh", "lint": "eslint . --ext .js,.ts,.tsx", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "keywords": ["blockchain", "layer1", "ai", "quantum", "zero-knowledge", "depin", "sovereign"], "author": "AETHIR-X Team", "license": "MIT", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "ethers": "^6.9.0", "ws": "^8.16.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "mongoose": "^8.0.3", "redis": "^4.6.11", "socket.io": "^4.7.4", "grpc": "^1.24.11", "@grpc/grpc-js": "^1.9.14", "@grpc/proto-loader": "^0.7.10"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/ws": "^8.5.10", "typescript": "^5.3.3", "ts-node": "^10.9.2", "nodemon": "^3.0.2", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^6.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/aethir-x/aethir-x.git"}, "bugs": {"url": "https://github.com/aethir-x/aethir-x/issues"}, "homepage": "https://aethir-x.org"}