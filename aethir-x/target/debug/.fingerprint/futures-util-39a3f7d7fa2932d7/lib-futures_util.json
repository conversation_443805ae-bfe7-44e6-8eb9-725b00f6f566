{"rustc": 8750902384292223123, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 4514183110521809768, "deps": [[5103565458935487, "futures_io", false, 17557918167133696770], [1615478164327904835, "pin_utils", false, 3359762141501452021], [1811549171721445101, "futures_channel", false, 2496345169861617656], [1906322745568073236, "pin_project_lite", false, 6482054633883229669], [3129130049864710036, "memchr", false, 3161043063022191973], [6955678925937229351, "slab", false, 11823115934647135953], [7013762810557009322, "futures_sink", false, 8102378011112250464], [7620660491849607393, "futures_core", false, 10043418134235967760], [10565019901765856648, "futures_macro", false, 10491703728230298292], [16240732885093539806, "futures_task", false, 1667809094504614873]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-39a3f7d7fa2932d7/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}