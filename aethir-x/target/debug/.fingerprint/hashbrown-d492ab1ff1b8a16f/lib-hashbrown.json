{"rustc": 8750902384292223123, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2241668132362809309, "path": 2237185529969766240, "deps": [[5230392855116717286, "equivalent", false, 17053591027124847608], [9150530836556604396, "allocator_api2", false, 8622746534926015030], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 12925833292645980188]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-d492ab1ff1b8a16f/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}