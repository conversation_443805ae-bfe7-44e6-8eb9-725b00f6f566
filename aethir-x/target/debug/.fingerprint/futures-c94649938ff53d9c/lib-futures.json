{"rustc": 8750902384292223123, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\", \"thread-pool\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17467636112133979524, "path": 6393280463850028111, "deps": [[5103565458935487, "futures_io", false, 17557918167133696770], [1811549171721445101, "futures_channel", false, 2496345169861617656], [7013762810557009322, "futures_sink", false, 8102378011112250464], [7620660491849607393, "futures_core", false, 10043418134235967760], [10629569228670356391, "futures_util", false, 6223495545674008630], [12779779637805422465, "futures_executor", false, 16667652096659066635], [16240732885093539806, "futures_task", false, 1667809094504614873]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-c94649938ff53d9c/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}