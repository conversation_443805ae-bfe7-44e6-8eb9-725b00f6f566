{"rustc": 8750902384292223123, "features": "[]", "declared_features": "[]", "target": 9772686344620435246, "profile": 3316208278650011218, "path": 6924945498109910595, "deps": [[2357570525450087091, "num_cpus", false, 12715796071882224933], [2706460456408817945, "futures", false, 8870215083179766767], [4495526598637097934, "parking_lot", false, 5648368750790287749], [5364813825765636762, "dashmap", false, 17150082773114045346], [8008191657135824715, "thiserror", false, 11899356130250849873], [8319709847752024821, "uuid", false, 2478060610527640660], [8606274917505247608, "tracing", false, 12732434382674110070], [9241925498456048256, "blake3", false, 9080980542305843806], [9538054652646069845, "tokio", false, 12045475093691616238], [9689903380558560274, "serde", false, 5233713539488721091], [9897246384292347999, "chrono", false, 11320866691924719965], [11946729385090170470, "async_trait", false, 2664265366419546902], [12382237672615274180, "config", false, 15489652011609810818], [13625485746686963219, "anyhow", false, 1552536555602977813], [15367738274754116744, "serde_json", false, 13108490614513771968], [15766163325958592597, "tokio_test", false, 14925293225411729030], [16230660778393187092, "tracing_subscriber", false, 9174056454365715489], [18435991034043862239, "crossbeam", false, 8711382729392368201]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/aethir-xos-66e4d12b7e27b8d8/dep-test-lib-aethir_xos", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}