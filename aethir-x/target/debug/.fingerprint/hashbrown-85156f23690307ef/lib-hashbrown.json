{"rustc": 8750902384292223123, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 18094005868126109150, "deps": [[966925859616469517, "ahash", false, 5864293948474944289], [9150530836556604396, "allocator_api2", false, 8622746534926015030]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-85156f23690307ef/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}