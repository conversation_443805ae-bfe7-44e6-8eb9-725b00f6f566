#!/bin/bash

# AETHIR-X Build Script
# Builds all components of the AETHIR-X system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[BUILD]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
BUILD_TYPE="release"
VERBOSE=false
CLEAN=false
FEATURES=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            BUILD_TYPE="debug"
            shift
            ;;
        --release)
            BUILD_TYPE="release"
            shift
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        --features)
            FEATURES="$2"
            shift 2
            ;;
        --help|-h)
            echo "AETHIR-X Build Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --debug          Build in debug mode (default: release)"
            echo "  --release        Build in release mode"
            echo "  --verbose, -v    Verbose output"
            echo "  --clean          Clean before building"
            echo "  --features FEAT  Enable specific features"
            echo "  --help, -h       Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Build configuration
if [[ "$BUILD_TYPE" == "release" ]]; then
    CARGO_FLAGS="--release"
    print_status "Building in RELEASE mode"
else
    CARGO_FLAGS=""
    print_status "Building in DEBUG mode"
fi

if [[ "$VERBOSE" == "true" ]]; then
    CARGO_FLAGS="$CARGO_FLAGS --verbose"
fi

if [[ -n "$FEATURES" ]]; then
    CARGO_FLAGS="$CARGO_FLAGS --features $FEATURES"
    print_status "Enabled features: $FEATURES"
fi

# Clean if requested
if [[ "$CLEAN" == "true" ]]; then
    print_status "Cleaning previous builds..."
    cargo clean
    rm -rf dist/
    rm -rf node_modules/.cache/
    print_success "Clean completed"
fi

# Create output directory
mkdir -p dist

print_status "Starting AETHIR-X build process..."

# Build Rust components
build_rust() {
    print_status "Building Rust components..."
    
    # Check code first
    print_status "Running cargo check..."
    cargo check --all-targets --all-features
    
    # Format code
    print_status "Formatting Rust code..."
    cargo fmt --all -- --check || {
        print_status "Auto-formatting code..."
        cargo fmt --all
    }
    
    # Run clippy
    print_status "Running clippy lints..."
    cargo clippy --all-targets --all-features -- -D warnings
    
    # Build all workspace members
    print_status "Building workspace..."
    cargo build $CARGO_FLAGS --all-targets
    
    # Build specific binaries
    print_status "Building AETHIR-XOS binary..."
    cargo build $CARGO_FLAGS --bin aethir-xos
    
    print_success "Rust components built successfully"
}

# Build Node.js components
build_nodejs() {
    if [[ -f "package.json" ]]; then
        print_status "Building Node.js components..."
        
        # Install dependencies if needed
        if [[ ! -d "node_modules" ]]; then
            print_status "Installing Node.js dependencies..."
            npm install
        fi
        
        # Build TypeScript
        if [[ -f "tsconfig.json" ]]; then
            print_status "Compiling TypeScript..."
            npx tsc
        fi
        
        # Run linting
        print_status "Running ESLint..."
        npx eslint . --ext .js,.ts,.tsx --fix
        
        # Build frontend if exists
        if [[ -d "ui-ux" ]]; then
            print_status "Building frontend..."
            cd ui-ux
            npm run build
            cd ..
        fi
        
        print_success "Node.js components built successfully"
    else
        print_status "No package.json found, skipping Node.js build"
    fi
}

# Build documentation
build_docs() {
    print_status "Building documentation..."
    
    # Generate Rust docs
    print_status "Generating Rust documentation..."
    cargo doc --all-features --no-deps
    
    # Copy docs to dist
    if [[ -d "target/doc" ]]; then
        cp -r target/doc dist/
        print_status "Rust documentation copied to dist/doc"
    fi
    
    # Build additional docs if they exist
    if [[ -d "docs" ]]; then
        cp -r docs dist/
        print_status "Additional documentation copied to dist/docs"
    fi
    
    print_success "Documentation built successfully"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Rust tests
    print_status "Running Rust tests..."
    cargo test --all-features
    
    # Node.js tests if they exist
    if [[ -f "package.json" ]] && grep -q "test" package.json; then
        print_status "Running Node.js tests..."
        npm test
    fi
    
    print_success "All tests passed"
}

# Create distribution package
create_dist() {
    print_status "Creating distribution package..."
    
    # Copy binaries
    if [[ "$BUILD_TYPE" == "release" ]]; then
        BINARY_DIR="target/release"
    else
        BINARY_DIR="target/debug"
    fi
    
    mkdir -p dist/bin
    
    # Copy main binary
    if [[ -f "$BINARY_DIR/aethir-xos" ]]; then
        cp "$BINARY_DIR/aethir-xos" dist/bin/
        print_status "Binary copied to dist/bin/"
    fi
    
    # Copy configuration
    if [[ -d "config" ]]; then
        cp -r config dist/
        print_status "Configuration copied to dist/config/"
    fi
    
    # Copy scripts
    mkdir -p dist/scripts
    cp scripts/*.sh dist/scripts/
    chmod +x dist/scripts/*.sh
    print_status "Scripts copied to dist/scripts/"
    
    # Create version info
    cat > dist/VERSION << EOF
AETHIR-X Version Information
===========================

Version: 0.1.0
Build Type: $BUILD_TYPE
Build Date: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
Git Commit: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
Rust Version: $(rustc --version)
Features: $FEATURES

Components:
- AETHIR-XOS (Core Operating System)
- X-Consensus (Quantum-Resilient Consensus)
- zkFlux VM (Zero-Knowledge Virtual Machine)
- NeuroForge (AI Orchestration Layer)
- XStealth (Privacy Layer)
- XNet (Interoperability Fabric)
- LUMINA Mesh (DePIN Infrastructure)
- SoulMesh (Decentralized Storage)
- Veritas Graph (Block Explorer)
- Arcanum Engine (Identity & Governance)
- NeuraDev (Developer Tools)
EOF
    
    print_success "Distribution package created in dist/"
}

# Generate build report
generate_report() {
    print_status "Generating build report..."
    
    REPORT_FILE="dist/build-report.txt"
    
    cat > "$REPORT_FILE" << EOF
AETHIR-X Build Report
====================

Build Configuration:
- Type: $BUILD_TYPE
- Features: ${FEATURES:-"default"}
- Timestamp: $(date -u +"%Y-%m-%d %H:%M:%S UTC")

Binary Sizes:
EOF
    
    if [[ -f "dist/bin/aethir-xos" ]]; then
        SIZE=$(ls -lh dist/bin/aethir-xos | awk '{print $5}')
        echo "- aethir-xos: $SIZE" >> "$REPORT_FILE"
    fi
    
    echo "" >> "$REPORT_FILE"
    echo "Cargo Dependencies:" >> "$REPORT_FILE"
    cargo tree --depth 1 >> "$REPORT_FILE" 2>/dev/null || echo "Unable to generate dependency tree" >> "$REPORT_FILE"
    
    print_success "Build report generated: $REPORT_FILE"
}

# Main build function
main() {
    echo "╔═══════════════════════════════════════════════════════════════╗"
    echo "║                    AETHIR-X Build System                     ║"
    echo "║           Exponential Sovereign Intelligence Substrate       ║"
    echo "╚═══════════════════════════════════════════════════════════════╝"
    echo
    
    START_TIME=$(date +%s)
    
    # Run build steps
    build_rust
    build_nodejs
    build_docs
    
    # Run tests unless in CI or explicitly disabled
    if [[ -z "$CI" ]] && [[ -z "$SKIP_TESTS" ]]; then
        run_tests
    fi
    
    create_dist
    generate_report
    
    END_TIME=$(date +%s)
    BUILD_TIME=$((END_TIME - START_TIME))
    
    echo
    print_success "🎉 AETHIR-X build completed successfully!"
    print_success "Build time: ${BUILD_TIME}s"
    echo
    echo "Output directory: dist/"
    echo "Binary location: dist/bin/aethir-xos"
    echo "Documentation: dist/doc/"
    echo "Build report: dist/build-report.txt"
    echo
    echo "To run AETHIR-X:"
    echo "  ./dist/bin/aethir-xos --help"
    echo
}

# Handle errors
trap 'print_error "Build failed at line $LINENO"' ERR

# Run main function
main "$@"
