#!/bin/bash

# AETHIR-X Setup Script
# Installs dependencies and prepares the development environment

set -e

echo "🚀 Setting up AETHIR-X development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_success "macOS detected"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Check if Rust is installed
check_rust() {
    print_status "Checking Rust installation..."
    
    if command -v rustc &> /dev/null; then
        RUST_VERSION=$(rustc --version)
        print_success "Rust found: $RUST_VERSION"
        
        # Check if version is recent enough
        RUST_VERSION_NUM=$(rustc --version | cut -d' ' -f2)
        if [[ "$RUST_VERSION_NUM" < "1.70.0" ]]; then
            print_warning "Rust version $RUST_VERSION_NUM is older than recommended (1.70.0+)"
            print_status "Updating Rust..."
            rustup update
        fi
    else
        print_status "Rust not found. Installing Rust..."
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
        source ~/.cargo/env
        print_success "Rust installed successfully"
    fi
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
        
        # Check if version is recent enough
        NODE_VERSION_NUM=$(node --version | cut -d'v' -f2)
        if [[ "$NODE_VERSION_NUM" < "18.0.0" ]]; then
            print_warning "Node.js version $NODE_VERSION_NUM is older than recommended (18.0.0+)"
        fi
    else
        print_status "Node.js not found. Please install Node.js 18+ manually"
        print_status "Visit: https://nodejs.org/"
        exit 1
    fi
}

# Install system dependencies
install_system_deps() {
    print_status "Installing system dependencies..."
    
    if [[ "$OS" == "linux" ]]; then
        # Detect Linux distribution
        if command -v apt-get &> /dev/null; then
            print_status "Installing dependencies with apt..."
            sudo apt-get update
            sudo apt-get install -y \
                build-essential \
                pkg-config \
                libssl-dev \
                libclang-dev \
                cmake \
                git \
                curl \
                wget \
                unzip \
                protobuf-compiler \
                libprotobuf-dev
        elif command -v yum &> /dev/null; then
            print_status "Installing dependencies with yum..."
            sudo yum groupinstall -y "Development Tools"
            sudo yum install -y \
                openssl-devel \
                clang-devel \
                cmake \
                git \
                curl \
                wget \
                unzip \
                protobuf-compiler \
                protobuf-devel
        else
            print_warning "Unknown Linux distribution. Please install dependencies manually."
        fi
    elif [[ "$OS" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            print_status "Installing dependencies with Homebrew..."
            brew install \
                cmake \
                protobuf \
                openssl \
                pkg-config
        else
            print_warning "Homebrew not found. Please install dependencies manually."
        fi
    fi
    
    print_success "System dependencies installed"
}

# Install Rust components
install_rust_components() {
    print_status "Installing Rust components..."
    
    # Install required Rust components
    rustup component add clippy rustfmt
    rustup target add wasm32-unknown-unknown
    
    # Install cargo tools
    cargo install cargo-watch cargo-expand cargo-audit
    
    print_success "Rust components installed"
}

# Install Node.js dependencies
install_node_deps() {
    print_status "Installing Node.js dependencies..."
    
    if [[ -f "package.json" ]]; then
        npm install
        print_success "Node.js dependencies installed"
    else
        print_warning "package.json not found, skipping Node.js dependencies"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating project directories..."
    
    mkdir -p {
        config,
        logs,
        data,
        keys,
        docs/generated,
        tests/integration,
        examples,
        scripts/deploy,
        .github/workflows
    }
    
    print_success "Project directories created"
}

# Generate default configuration
generate_config() {
    print_status "Generating default configuration..."
    
    cat > config/default.toml << EOF
[network]
id = "aethir-x-testnet"
listen_addr = "0.0.0.0:8000"
bootstrap_nodes = []

[consensus]
module = "x-consensus"
block_time = 3000  # milliseconds
finality_time = 300000  # milliseconds

[vm]
modules = ["zkflux-vm", "evm-compat"]
gas_limit = 30000000

[ai]
modules = ["neuroforge", "chrono-scheduler"]
compute_limit = 1000000

[privacy]
modules = ["xstealth", "zk-proofs"]
default_privacy = true

[interop]
modules = ["xnet", "bridge-agents"]
supported_chains = ["ethereum", "solana", "cosmos"]

[storage]
data_dir = "./data"
max_size_gb = 100

[logging]
level = "info"
file = "./logs/aethir-x.log"
max_files = 10

[quantum]
enabled = true
entropy_source = "hardware"
algorithms = ["kyber", "dilithium"]
EOF
    
    print_success "Default configuration generated"
}

# Set up development environment
setup_dev_env() {
    print_status "Setting up development environment..."
    
    # Create .env file for development
    cat > .env << EOF
RUST_LOG=info
AETHIR_X_ENV=development
AETHIR_X_CONFIG=config/default.toml
EOF
    
    # Create VS Code settings
    mkdir -p .vscode
    cat > .vscode/settings.json << EOF
{
    "rust-analyzer.cargo.features": "all",
    "rust-analyzer.checkOnSave.command": "clippy",
    "editor.formatOnSave": true,
    "files.associations": {
        "*.rs": "rust"
    }
}
EOF
    
    print_success "Development environment configured"
}

# Run initial build
initial_build() {
    print_status "Running initial build..."
    
    # Check Rust code
    cargo check --all-targets --all-features
    
    # Format code
    cargo fmt --all
    
    # Run clippy
    cargo clippy --all-targets --all-features -- -D warnings
    
    print_success "Initial build completed"
}

# Main setup function
main() {
    echo "╔═══════════════════════════════════════════════════════════════╗"
    echo "║                    AETHIR-X Setup Script                     ║"
    echo "║           Exponential Sovereign Intelligence Substrate       ║"
    echo "╚═══════════════════════════════════════════════════════════════╝"
    echo
    
    check_os
    check_rust
    check_nodejs
    install_system_deps
    install_rust_components
    install_node_deps
    create_directories
    generate_config
    setup_dev_env
    initial_build
    
    echo
    print_success "🎉 AETHIR-X setup completed successfully!"
    echo
    echo "Next steps:"
    echo "  1. Review the configuration in config/default.toml"
    echo "  2. Run 'cargo run' to start the system"
    echo "  3. Run 'cargo test' to run tests"
    echo "  4. Check out the documentation in docs/"
    echo
    echo "For development:"
    echo "  - Use 'cargo watch -x run' for auto-reloading"
    echo "  - Use 'cargo clippy' for linting"
    echo "  - Use 'cargo fmt' for formatting"
    echo
}

# Run main function
main "$@"
