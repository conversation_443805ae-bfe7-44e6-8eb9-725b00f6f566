#!/bin/bash

# AETHIR-X Development Script
# Starts the development environment with hot reloading

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[DEV]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default configuration
MODE="watch"
COMPONENT="all"
PORT=8000
LOG_LEVEL="debug"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --mode)
            MODE="$2"
            shift 2
            ;;
        --component)
            COMPONENT="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        --help|-h)
            echo "AETHIR-X Development Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --mode MODE        Development mode (watch|run|test) [default: watch]"
            echo "  --component COMP   Component to run (all|core|ai|privacy|interop) [default: all]"
            echo "  --port PORT        Port to listen on [default: 8000]"
            echo "  --log-level LEVEL  Log level (trace|debug|info|warn|error) [default: debug]"
            echo "  --help, -h         Show this help message"
            echo ""
            echo "Modes:"
            echo "  watch    - Watch for changes and auto-reload"
            echo "  run      - Run once without watching"
            echo "  test     - Run in test mode with continuous testing"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Set environment variables
export RUST_LOG="$LOG_LEVEL"
export AETHIR_X_ENV="development"
export AETHIR_X_PORT="$PORT"
export AETHIR_X_CONFIG="config/default.toml"

# Create development config if it doesn't exist
create_dev_config() {
    if [[ ! -f "config/development.toml" ]]; then
        print_status "Creating development configuration..."
        
        cat > config/development.toml << EOF
[network]
id = "aethir-x-devnet"
listen_addr = "127.0.0.1:$PORT"
bootstrap_nodes = []

[consensus]
module = "x-consensus"
block_time = 1000  # faster for development
finality_time = 5000  # faster for development

[vm]
modules = ["zkflux-vm"]
gas_limit = 10000000  # reduced for development

[ai]
modules = ["neuroforge"]
compute_limit = 100000  # reduced for development

[privacy]
modules = ["xstealth"]
default_privacy = false  # disabled for easier debugging

[interop]
modules = ["xnet"]
supported_chains = ["ethereum"]  # reduced for development

[storage]
data_dir = "./data/dev"
max_size_gb = 10  # reduced for development

[logging]
level = "$LOG_LEVEL"
file = "./logs/aethir-x-dev.log"
max_files = 5

[quantum]
enabled = false  # disabled for development
EOF
        
        export AETHIR_X_CONFIG="config/development.toml"
        print_success "Development configuration created"
    fi
}

# Start development environment
start_dev_env() {
    print_status "Starting AETHIR-X development environment..."
    print_status "Mode: $MODE"
    print_status "Component: $COMPONENT"
    print_status "Port: $PORT"
    print_status "Log Level: $LOG_LEVEL"
    echo
    
    # Create necessary directories
    mkdir -p {data/dev,logs,keys/dev}
    
    # Create development config
    create_dev_config
    
    case "$MODE" in
        "watch")
            start_watch_mode
            ;;
        "run")
            start_run_mode
            ;;
        "test")
            start_test_mode
            ;;
        *)
            print_error "Unknown mode: $MODE"
            exit 1
            ;;
    esac
}

# Watch mode with auto-reload
start_watch_mode() {
    print_status "Starting watch mode with auto-reload..."
    
    # Check if cargo-watch is installed
    if ! command -v cargo-watch &> /dev/null; then
        print_warning "cargo-watch not found. Installing..."
        cargo install cargo-watch
    fi
    
    case "$COMPONENT" in
        "all"|"core")
            print_status "Watching core AETHIR-XOS..."
            cargo watch -x "run --bin aethir-xos -- --dev --config $AETHIR_X_CONFIG"
            ;;
        "ai")
            print_status "Watching AI components..."
            cargo watch -w ai/ -x "test -p neuroforge"
            ;;
        "privacy")
            print_status "Watching privacy components..."
            cargo watch -w privacy/ -x "test -p xstealth"
            ;;
        "interop")
            print_status "Watching interop components..."
            cargo watch -w interop/ -x "test -p xnet"
            ;;
        *)
            print_error "Unknown component: $COMPONENT"
            exit 1
            ;;
    esac
}

# Run mode (single execution)
start_run_mode() {
    print_status "Running AETHIR-X in development mode..."
    
    # Build first
    cargo build --bin aethir-xos
    
    # Run with development flags
    cargo run --bin aethir-xos -- \
        --dev \
        --config "$AETHIR_X_CONFIG" \
        --network "aethir-x-devnet" \
        --quantum=false
}

# Test mode with continuous testing
start_test_mode() {
    print_status "Starting continuous test mode..."
    
    # Check if cargo-watch is installed
    if ! command -v cargo-watch &> /dev/null; then
        print_warning "cargo-watch not found. Installing..."
        cargo install cargo-watch
    fi
    
    case "$COMPONENT" in
        "all")
            print_status "Running all tests continuously..."
            cargo watch -x "test --all-features"
            ;;
        "core")
            print_status "Testing core components..."
            cargo watch -w core/ -x "test -p aethir-xos"
            ;;
        "ai")
            print_status "Testing AI components..."
            cargo watch -w ai/ -x "test -p neuroforge -p agents"
            ;;
        "privacy")
            print_status "Testing privacy components..."
            cargo watch -w privacy/ -x "test -p xstealth"
            ;;
        "interop")
            print_status "Testing interop components..."
            cargo watch -w interop/ -x "test -p xnet"
            ;;
        *)
            print_error "Unknown component: $COMPONENT"
            exit 1
            ;;
    esac
}

# Setup development tools
setup_dev_tools() {
    print_status "Setting up development tools..."
    
    # Install useful cargo tools
    TOOLS=(
        "cargo-watch"
        "cargo-expand"
        "cargo-audit"
        "cargo-outdated"
        "cargo-tree"
        "cargo-bloat"
    )
    
    for tool in "${TOOLS[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            print_status "Installing $tool..."
            cargo install "$tool"
        fi
    done
    
    print_success "Development tools installed"
}

# Show development status
show_dev_status() {
    echo
    echo "╔═══════════════════════════════════════════════════════════════╗"
    echo "║                 AETHIR-X Development Status                  ║"
    echo "╚═══════════════════════════════════════════════════════════════╝"
    echo
    
    print_status "Environment: Development"
    print_status "Config: $AETHIR_X_CONFIG"
    print_status "Port: $PORT"
    print_status "Log Level: $LOG_LEVEL"
    echo
    
    print_status "Available endpoints:"
    echo "  - Main API: http://localhost:$PORT"
    echo "  - Health: http://localhost:$PORT/health"
    echo "  - Metrics: http://localhost:$PORT/metrics"
    echo "  - Docs: http://localhost:$PORT/docs"
    echo
    
    print_status "Development commands:"
    echo "  - cargo test           # Run tests"
    echo "  - cargo clippy         # Run lints"
    echo "  - cargo fmt            # Format code"
    echo "  - cargo doc --open     # Open documentation"
    echo
    
    print_status "Logs location: logs/aethir-x-dev.log"
    print_status "Data location: data/dev/"
    echo
}

# Cleanup function
cleanup() {
    print_status "Cleaning up development environment..."
    
    # Kill any background processes
    jobs -p | xargs -r kill
    
    print_success "Development environment cleaned up"
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Main function
main() {
    echo "╔═══════════════════════════════════════════════════════════════╗"
    echo "║                AETHIR-X Development Environment              ║"
    echo "║           Exponential Sovereign Intelligence Substrate       ║"
    echo "╚═══════════════════════════════════════════════════════════════╝"
    echo
    
    # Setup development tools if needed
    if [[ ! -f ".dev-tools-installed" ]]; then
        setup_dev_tools
        touch .dev-tools-installed
    fi
    
    show_dev_status
    start_dev_env
}

# Run main function
main "$@"
