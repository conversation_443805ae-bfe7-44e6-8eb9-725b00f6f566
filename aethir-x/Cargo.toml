[workspace]
members = [
    "core/aethir-xos",
    "core/consensus",
]

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["AETHIR-X Team"]
license = "MIT"
repository = "https://github.com/aethir-x/aethir-x"

[workspace.dependencies]
# Core dependencies
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"

# Cryptography
ed25519-dalek = "2.0"
sha3 = "0.10"
blake3 = "1.5"
ring = "0.17"
aes-gcm = "0.10"

# Networking
libp2p = "0.53"
quinn = "0.10"
hyper = { version = "1.0", features = ["full"] }
tonic = "0.11"

# Database
rocksdb = "0.21"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "sqlite"] }

# Zero-knowledge
ark-ff = "0.4"
ark-ec = "0.4"
ark-serialize = "0.4"
ark-std = "0.4"
halo2_proofs = "0.3"

# AI/ML
candle-core = "0.4"
candle-nn = "0.4"
tch = "0.13"

# Quantum-resistant cryptography
pqcrypto-kyber = "0.7"
pqcrypto-dilithium = "0.5"

# WebAssembly
wasmtime = "17.0"
wasmer = "4.2"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
clap = { version = "4.0", features = ["derive"] }
config = "0.14"
dashmap = "5.5"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true
