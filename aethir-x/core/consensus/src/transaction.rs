//! Transaction structures for consensus

use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use blake3;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Transaction {
    pub id: Uuid,
    pub hash: String,
    pub from: String,
    pub to: String,
    pub amount: u64,
    pub fee: u64,
    pub nonce: u64,
    pub timestamp: DateTime<Utc>,
    pub signature: Vec<u8>,
    pub data: Vec<u8>,
}

impl Transaction {
    pub fn new(
        from: String,
        to: String,
        amount: u64,
        fee: u64,
        nonce: u64,
        data: Vec<u8>,
    ) -> Self {
        let id = Uuid::new_v4();
        let timestamp = Utc::now();
        
        // Calculate transaction hash
        let tx_data = format!("{}{}{}{}{}{}", 
            from, to, amount, fee, nonce, timestamp.timestamp());
        let hash = blake3::hash(tx_data.as_bytes()).to_hex().to_string();

        Transaction {
            id,
            hash,
            from,
            to,
            amount,
            fee,
            nonce,
            timestamp,
            signature: vec![], // TODO: Add actual signature
            data,
        }
    }

    pub fn verify(&self) -> bool {
        // TODO: Implement transaction verification
        true
    }
}
