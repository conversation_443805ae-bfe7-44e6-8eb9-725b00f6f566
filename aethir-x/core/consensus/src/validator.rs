//! Validator management for consensus

use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Validator {
    pub id: Uuid,
    pub public_key: Vec<u8>,
    pub stake: u64,
    pub reputation: f64,
    pub last_active: DateTime<Utc>,
}

#[derive(Debu<PERSON>, Clone)]
pub struct ValidatorSet {
    pub validators: HashMap<Uuid, Validator>,
    pub total_stake: u64,
}

impl ValidatorSet {
    pub fn new() -> Self {
        Self {
            validators: HashMap::new(),
            total_stake: 0,
        }
    }

    pub fn add_validator(&mut self, validator: Validator) {
        self.total_stake += validator.stake;
        self.validators.insert(validator.id, validator);
    }

    pub fn remove_validator(&mut self, id: &Uuid) -> Option<Validator> {
        if let Some(validator) = self.validators.remove(id) {
            self.total_stake -= validator.stake;
            Some(validator)
        } else {
            None
        }
    }

    pub fn get_validator(&self, id: &Uuid) -> Option<&Validator> {
        self.validators.get(id)
    }

    pub fn active_validators(&self) -> Vec<&Validator> {
        let cutoff = Utc::now() - chrono::Duration::hours(1);
        self.validators.values()
            .filter(|v| v.last_active > cutoff)
            .collect()
    }
}
