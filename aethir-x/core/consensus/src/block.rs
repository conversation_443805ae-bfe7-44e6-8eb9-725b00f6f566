//! Block structures for consensus

use anyhow::Result;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use blake3;

use crate::transaction::Transaction;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Block {
    pub header: BlockHeader,
    pub transactions: Vec<Transaction>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BlockHeader {
    pub height: u64,
    pub hash: String,
    pub previous_hash: String,
    pub merkle_root: String,
    pub state_root: String,
    pub timestamp: DateTime<Utc>,
    pub proposer_id: Uuid,
    pub signature: Vec<u8>,
    pub nonce: u64,
}

impl Block {
    pub fn new(
        height: u64,
        transactions: Vec<Transaction>,
        proposer_id: Uuid,
        _max_size: usize,
    ) -> Result<Self> {
        let timestamp = Utc::now();
        let merkle_root = Self::calculate_merkle_root(&transactions);
        let previous_hash = "previous".to_string(); // TODO: Get actual previous hash
        let state_root = "state".to_string(); // TODO: Calculate actual state root
        
        // Calculate block hash
        let header_data = format!("{}{}{}{}{}", 
            height, previous_hash, merkle_root, state_root, timestamp.timestamp());
        let hash = blake3::hash(header_data.as_bytes()).to_hex().to_string();

        let header = BlockHeader {
            height,
            hash,
            previous_hash,
            merkle_root,
            state_root,
            timestamp,
            proposer_id,
            signature: vec![], // TODO: Add actual signature
            nonce: 0,
        };

        Ok(Block {
            header,
            transactions,
        })
    }

    fn calculate_merkle_root(transactions: &[Transaction]) -> String {
        if transactions.is_empty() {
            return "empty".to_string();
        }

        // Simple merkle root calculation
        let tx_hashes: Vec<String> = transactions.iter()
            .map(|tx| tx.hash.clone())
            .collect();
        
        let combined = tx_hashes.join("");
        blake3::hash(combined.as_bytes()).to_hex().to_string()
    }

    pub fn verify(&self) -> bool {
        // TODO: Implement block verification
        true
    }
}
