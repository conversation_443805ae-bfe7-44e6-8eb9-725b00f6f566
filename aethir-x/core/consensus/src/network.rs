//! Network layer for consensus

use anyhow::Result;
use tracing::{info, debug};

use crate::ConsensusConfig;

#[derive(Debug)]
pub struct NetworkLayer {
    config: ConsensusConfig,
    peers: Vec<String>,
}

impl NetworkLayer {
    pub async fn new(config: &ConsensusConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            peers: Vec::new(),
        })
    }

    pub async fn start(&self) -> Result<()> {
        info!("Starting consensus network layer");
        debug!("Network ID: {}", self.config.network_id);
        Ok(())
    }

    pub async fn stop(&self) -> Result<()> {
        info!("Stopping consensus network layer");
        Ok(())
    }

    pub fn add_peer(&mut self, peer: String) {
        self.peers.push(peer);
    }

    pub fn get_peers(&self) -> &[String] {
        &self.peers
    }
}
