//! AETHIR-XOS Main Entry Point
//! 
//! Bootstraps and runs the AETHIR-X operating system with all core components.

use anyhow::Result;
use clap::{Arg, Command};
use tracing::{info, error, Level};
use tracing_subscriber;
use tokio::signal;
use std::sync::Arc;

use aethir_xos::{AethirXOS, SystemConfig};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_target(false)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true)
        .init();

    // Parse command line arguments
    let matches = Command::new("aethir-xos")
        .version("0.1.0")
        .author("AETHIR-X Team")
        .about("Exponential Sovereign Intelligence Substrate Operating System")
        .arg(
            Arg::new("config")
                .short('c')
                .long("config")
                .value_name("FILE")
                .help("Configuration file path")
                .default_value("config/default.toml"),
        )
        .arg(
            Arg::new("node-id")
                .long("node-id")
                .value_name("UUID")
                .help("Node identifier (UUID)")
        )
        .arg(
            Arg::new("network")
                .short('n')
                .long("network")
                .value_name("NETWORK")
                .help("Network to connect to")
                .default_value("aethir-x-mainnet"),
        )
        .arg(
            Arg::new("consensus")
                .long("consensus")
                .value_name("MODULE")
                .help("Consensus module to use")
                .default_value("x-consensus"),
        )
        .arg(
            Arg::new("quantum")
                .long("quantum")
                .help("Enable quantum-ready features")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("dev-mode")
                .long("dev")
                .help("Run in development mode")
                .action(clap::ArgAction::SetTrue),
        )
        .get_matches();

    // Load configuration
    let config_path = matches.get_one::<String>("config").unwrap();
    let mut config = load_config(config_path).await?;

    // Override config with command line arguments
    if let Some(node_id) = matches.get_one::<String>("node-id") {
        config.node_id = node_id.parse()?;
    }

    if let Some(network) = matches.get_one::<String>("network") {
        config.network_id = network.clone();
    }

    if let Some(consensus) = matches.get_one::<String>("consensus") {
        config.consensus_module = consensus.clone();
    }

    if matches.get_flag("quantum") {
        config.quantum_ready = true;
    }

    // Development mode adjustments
    if matches.get_flag("dev-mode") {
        info!("Running in development mode");
        config.max_concurrent_tasks = 1000; // Reduced for dev
        config.temporal_partitions = 8; // Reduced for dev
    }

    // Print startup banner
    print_banner();

    // Initialize and start AETHIR-XOS
    info!("Initializing AETHIR-XOS with config: {:?}", config);
    let xos = Arc::new(AethirXOS::new(config).await?);

    info!("Starting AETHIR-XOS...");
    xos.start().await?;

    // Print system status
    let status = xos.get_status().await;
    info!("AETHIR-XOS started successfully!");
    info!("Node ID: {}", status.uptime);
    info!("Active modules: {}", status.active_modules);
    info!("Current TPS: {:.2}", status.tps_current);
    info!("Memory usage: {} MB", status.memory_usage / 1024 / 1024);

    // Set up graceful shutdown
    let xos_shutdown = xos.clone();
    tokio::spawn(async move {
        if let Err(e) = signal::ctrl_c().await {
            error!("Failed to listen for shutdown signal: {}", e);
            return;
        }

        info!("Shutdown signal received, stopping AETHIR-XOS...");
        if let Err(e) = xos_shutdown.shutdown().await {
            error!("Error during shutdown: {}", e);
        } else {
            info!("AETHIR-XOS shutdown complete");
        }

        std::process::exit(0);
    });

    // Main event loop
    let mut status_interval = tokio::time::interval(tokio::time::Duration::from_secs(30));
    
    loop {
        status_interval.tick().await;
        
        // Print periodic status updates
        let status = xos.get_status().await;
        info!(
            "Status - TPS: {:.2}, Memory: {} MB, Peers: {}, Uptime: {}s",
            status.tps_current,
            status.memory_usage / 1024 / 1024,
            status.network_peers,
            status.uptime.timestamp() - chrono::Utc::now().timestamp()
        );

        // Check for quantum entropy levels
        if status.quantum_entropy_level > 0.0 {
            info!("Quantum entropy level: {:.4}", status.quantum_entropy_level);
        }
    }
}

/// Load system configuration
async fn load_config(config_path: &str) -> Result<SystemConfig> {
    info!("Loading configuration from: {}", config_path);

    // For now, return default config
    // TODO: Implement actual config file loading
    let config = SystemConfig::default();
    
    info!("Configuration loaded successfully");
    Ok(config)
}

/// Print startup banner
fn print_banner() {
    println!(r#"
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║     █████╗ ███████╗████████╗██╗  ██╗██╗██████╗       ██╗  ██╗ ║
    ║    ██╔══██╗██╔════╝╚══██╔══╝██║  ██║██║██╔══██╗      ╚██╗██╔╝ ║
    ║    ███████║█████╗     ██║   ███████║██║██████╔╝       ╚███╔╝  ║
    ║    ██╔══██║██╔══╝     ██║   ██╔══██║██║██╔══██╗       ██╔██╗  ║
    ║    ██║  ██║███████╗   ██║   ██║  ██║██║██║  ██║      ██╔╝ ██╗ ║
    ║    ╚═╝  ╚═╝╚══════╝   ╚═╝   ╚═╝  ╚═╝╚═╝╚═╝  ╚═╝      ╚═╝  ╚═╝ ║
    ║                                                               ║
    ║           Exponential Sovereign Intelligence Substrate       ║
    ║                                                               ║
    ║    🚀 Quantum-Ready Layer-1 Blockchain Operating System      ║
    ║    🧠 AI-Native Architecture with Recursive Agents           ║
    ║    🔒 Zero-Knowledge Everything with Privacy by Design        ║
    ║    🌐 Native Interoperability without Third-Party Bridges    ║
    ║    ⚡ 2.5M+ TPS with <300ms Finality                         ║
    ║                                                               ║
    ║                   No third-party. No ceiling.                ║
    ║                   Just exponential sovereignty.              ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
    "#);
    
    println!("Version: 0.1.0");
    println!("Build: {}", env!("CARGO_PKG_VERSION"));
    println!("Rust: {}", env!("RUSTC_VERSION"));
    println!();
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_config_loading() {
        let config = load_config("nonexistent.toml").await;
        assert!(config.is_ok()); // Should return default config
    }

    #[test]
    fn test_banner_printing() {
        // Just ensure it doesn't panic
        print_banner();
    }
}
