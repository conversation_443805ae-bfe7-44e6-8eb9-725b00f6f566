import React, { useState } from 'react';
import { Button } from './UI';
import nwalletLogo from '../../projects/Logos/N-Wallet cropped.png';

interface NijaWalletConnectProps {
  onConnect?: (address: string) => void;
}

const NijaWalletConnect: React.FC<NijaWalletConnectProps> = ({ onConnect }) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [address, setAddress] = useState<string | null>(null);

  const handleConnect = () => {
    setIsConnecting(true);
    
    // Example account - in a real implementation, this would generate or fetch a wallet address
    const sampleAddress = '0x' + Array.from({length: 40}, () => 
      Math.floor(Math.random() * 16).toString(16)).join('');
    
    // Set the account in our provider
    if (window.ethereum?._isCustomNijaImplementation) {
      (window.ethereum as any).setAccounts([sampleAddress]);
      
      // Store for future use
      localStorage.setItem('nija_eth_address', sampleAddress);
      localStorage.setItem('nija_chain_id', '0xaa36a7'); // Sepolia testnet
      
      setAddress(sampleAddress);
      if (onConnect) onConnect(sampleAddress);
    }
    
    setIsConnecting(false);
  };

  // Listen for connection request events
  React.useEffect(() => {
    const handleRequestAccounts = () => {
      if (!address) {
        handleConnect();
      }
    };
    
    window.addEventListener('nijaWalletRequestAccounts', handleRequestAccounts);
    
    return () => {
      window.removeEventListener('nijaWalletRequestAccounts', handleRequestAccounts);
    };
  }, [address]);

  // Check for existing connection on mount
  React.useEffect(() => {
    const storedAddress = localStorage.getItem('nija_eth_address');
    if (storedAddress && window.ethereum?._isCustomNijaImplementation) {
      (window.ethereum as any).setAccounts([storedAddress]);
      setAddress(storedAddress);
      if (onConnect) onConnect(storedAddress);
    }
  }, []);

  return (
    <div className="nija-wallet-connect">
      {address ? (
        <div className="connected-state">
          <div className="account-info">
            <div className="address">
              {address.substring(0, 6)}...{address.substring(address.length - 4)}
            </div>
            <div className="network">Sepolia Testnet</div>
          </div>
        </div>
      ) : (
        <Button 
          onClick={handleConnect} 
          disabled={isConnecting}
          variant="primary"
          className="flex items-center gap-2"
        >
          <img 
            src={nwalletLogo} 
            alt="N-Wallet Logo" 
            className="h-6 w-auto"
          />
          {isConnecting ? 'Connecting...' : 'Connect'}
        </Button>
      )}
    </div>
  );
};

export default NijaWalletConnect; 