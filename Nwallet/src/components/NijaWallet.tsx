import { useWallet } from '../hooks/useWallet';
import nwalletLogo from '../../projects/Logos/N-Wallet cropped.png';

const NijaWallet = () => {
  const { ethAddress, ethChainId } = useWallet();
  
  const handleNFTGenClick = () => {
    // Create a session with wallet data
    const sessionData = {
      address: ethAddress,
      chainId: ethChainId,
      timestamp: Date.now()
    };
    
    // Store session in localStorage
    localStorage.setItem('nija_wallet_session', JSON.stringify(sessionData));
    
    // Open NFTGen with session parameter
    const sessionParam = encodeURIComponent(JSON.stringify(sessionData));
    window.open(`http://***********/nftgen?session=${sessionParam}`, '_blank');
  };

  return (
    <div className="flex items-center gap-2">
      <img 
        src={nwalletLogo} 
        alt="N-Wallet Logo" 
        className="h-8 w-auto"
      />
      <div className="text-sm text-gray-600">
        {ethAddress ? `${ethAddress.substring(0, 6)}...${ethAddress.substring(ethAddress.length - 4)}` : 'Not Connected'}
      </div>
    </div>
  );
};

export default NijaWallet; 