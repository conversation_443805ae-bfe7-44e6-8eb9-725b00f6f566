import React, { useState, useEffect } from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Box,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  SelectChangeEvent,
  CircularProgress,
  Button,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import LockIcon from '@mui/icons-material/Lock';
// import LockOpenIcon from '@mui/icons-material/LockOpen';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import LogoutIcon from '@mui/icons-material/Logout';
import { Link as RouterLink } from 'react-router-dom';
import { networks, switchNetwork, disconnectWallet } from '../../walletConnection';
import { useWallet } from '../../context/WalletContext';
import nwalletLogo from '../../../../Logos/N-Wallet cropped.png'; // Corrected import the logo path

interface HeaderProps {}

const Header: React.FC<HeaderProps> = () => {
  const theme = useTheme();
  const { ethAddress, ethChainId } = useWallet();
  const [selectedNetwork, setSelectedNetwork] = useState<keyof typeof networks>('sepolia');
  const [isSwitchingNetwork, setIsSwitchingNetwork] = useState(false);

  useEffect(() => {
    const handleChainChanged = (event: CustomEvent) => {
      const chainId = event.detail.chainId;
      const networkEntry = Object.entries(networks).find(([_, config]) =>
        ('chainId' in config) && config.chainId === chainId
      );
      if (networkEntry) {
        setSelectedNetwork(networkEntry[0] as keyof typeof networks);
      }
    };

    window.addEventListener('ethChainChanged', handleChainChanged as EventListener);

    return () => {
      window.removeEventListener('ethChainChanged', handleChainChanged as EventListener);
    };
  }, []);

  const handleNetworkChange = async (event: SelectChangeEvent) => {
    const newNetworkKey = event.target.value as keyof typeof networks;
    if (newNetworkKey === selectedNetwork) return;

    try {
      setIsSwitchingNetwork(true);
      await switchNetwork(newNetworkKey);
      setSelectedNetwork(newNetworkKey);
    } catch (error) {
      console.error('Failed to switch network:', error);
    } finally {
      setIsSwitchingNetwork(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnectWallet();
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
    }
  };

  return (
    <AppBar position="static" sx={{ backgroundColor: '#1a202c', zIndex: theme.zIndex.drawer + 1 }}>
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AccountBalanceWalletIcon sx={{ color: 'white' }} />
          <img 
            src={nwalletLogo} 
            alt="N-Wallet Logo" 
            style={{ height: '48px', marginRight: '20px' }}
          />
        </Box>

        <Box sx={{ flexGrow: 1 }} />

        {ethAddress && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <FormControl
              size="small"
              sx={{
                minWidth: 120,
                '& .MuiOutlinedInput-root': {
                  color: 'white',
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.87)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: theme.palette.primary.main,
                  },
                },
                '& .MuiSvgIcon-root': {
                  color: 'white',
                },
              }}
            >
              <Select
                value={selectedNetwork}
                onChange={handleNetworkChange}
                displayEmpty
                sx={{ color: 'white' }}
                disabled={isSwitchingNetwork}
                IconComponent={isSwitchingNetwork ? () => <CircularProgress size={20} sx={{ mr: 1 }} /> : undefined}
              >
                {Object.entries(networks).map(([id, config]) => (
                  <MenuItem key={id} value={id}>
                    {('chainName' in config) ? config.chainName : config.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Typography
              variant="body2"
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                padding: '4px 8px',
                borderRadius: 1,
                fontSize: '0.75rem',
              }}
            >
              {ethAddress.slice(0, 6)}...{ethAddress.slice(-4)}
            </Typography>

            <IconButton
              color="inherit"
              onClick={handleDisconnect}
              size="small"
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                },
              }}
            >
              <LogoutIcon />
            </IconButton>

            <Button
              component={RouterLink}
              to="/parental-control"
              color="inherit"
              startIcon={<LockIcon />}
              size="small"
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                },
                ml: 1,
                textTransform: 'none',
              }}
            >
              Parental Controls
            </Button>
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default Header;