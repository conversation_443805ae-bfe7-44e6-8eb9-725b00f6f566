import React from 'react';
import Button from './UI/Button';
import { useWeb3Modal } from '@web3modal/wagmi/react';
import { useTheme } from '../contexts/ThemeContext';
import { useConnect } from 'wagmi';
import { CodeIcon } from '@heroicons/react/24/outline';
import nwalletLogo from '../../projects/Logos/N-Wallet cropped.png';

interface HeaderProps {
  showParentalControl: boolean;
  onToggleParentalControl: () => void;
}

const Header: React.FC<HeaderProps> = ({ showParentalControl, onToggleParentalControl }) => {
  const { open } = useWeb3Modal();
  const { toggleTheme } = useTheme();
  const { connect, connectors, isConnected } = useConnect();

  const handleNFTGenClick = async () => {
    if (!isConnected) {
      open(); // Open Web3Modal to connect
    }
    // Redirect to our NFTGen instance
    window.open('http://3.111.22.56/nftgen', '_blank');
  };

  return (
    <div className="fixed top-0 left-0 right-0 p-4 shadow-lg z-50 dark:bg-background bg-gradient-to-r from-purple-500 to-pink-500">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center gap-4">
          <img 
            src={nwalletLogo} 
            alt="N-Wallet Logo" 
            className="h-12 w-auto mr-4"
          />
          <Button variant="web3outline" onClick={() => { /* Browser Logic */ }}>
            Browser
          </Button>
          <Button variant="web3outline" onClick={() => window.open('http://3.111.22.56/scgen', '_blank')}>
            SCGen
          </Button>
          <Button variant="web3outline" onClick={handleNFTGenClick}>
            NFTGen
          </Button>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="web3outline"
            onClick={toggleTheme}
            className="px-6 py-3 text-sm md:text-base bg-white bg-opacity-20 backdrop-blur-md text-white"
          >
            Toggle Theme
          </Button>
          <Button
            variant="web3outline"
            onClick={() => open()}
            className="px-6 py-3 text-sm md:text-base bg-white bg-opacity-20 backdrop-blur-md text-white"
          >
            Connect Wallet
          </Button>
          <Button
            variant="web3outline"
            onClick={onToggleParentalControl}
            className="px-6 py-3 text-sm md:text-base bg-white bg-opacity-20 backdrop-blur-md text-white"
          >
            {showParentalControl ? "Parental Control: ON" : "Parental Control: OFF"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Header;