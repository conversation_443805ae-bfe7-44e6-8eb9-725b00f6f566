import React, { Suspense } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Canvas } from '@react-three/fiber';
import * as THREE from 'three';
import AuroraBackground from '../components/AuroraBackground';
import { 
  WalletIcon, 
  CodeIcon,
  ShieldIcon, 
  PhoneIcon, 
  LockIcon, 
  PeopleIcon, 
  ChevronForwardIcon, 
  createIcon 
} from '../components/IconWrapper';

// Add any missing icons using createIcon before using them
const CodeSlashIcon = createIcon('CodeSlashOutline');
const PhonePortraitIcon = createIcon('PhonePortraitOutline');

interface Feature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

const features: Feature[] = [
  {
    title: 'Multi-Chain Support',
    description: 'Manage assets on Ethereum (Sepolia) and Solana (Devnet) in one place.',
    icon: CodeSlashIcon ? <CodeSlashIcon color={'#3B82F6'} height="36px" width="36px" /> : null
  },
  {
    title: 'Deterministic Keys',
    description: 'Your unique ETH & SOL addresses are generated from your account, ensuring consistency.',
    icon: <LockIcon color={'#3B82F6'} height="36px" width="36px" />
  },
  {
    title: 'Cross-Device Access',
    description: 'Log in on any device and access the same wallet addresses associated with your email.',
    icon: PhonePortraitIcon ? <PhonePortraitIcon color={'#3B82F6'} height="36px" width="36px" /> : null
  },
  {
    title: 'Security Focused',
    description: 'Leveraging standard cryptographic practices for secure key generation and storage.',
    icon: <ShieldIcon color={'#3B82F6'} height="36px" width="36px" />
  },
  {
    title: 'Parental Controls',
    description: 'Built-in features to help manage and safeguard family assets.',
    icon: <PeopleIcon color={'#3B82F6'} height="36px" width="36px" />
  },
  {
    title: 'True Ownership',
    description: 'You control your keys, you control your crypto. Non-custodial by design.',
    icon: <WalletIcon color={'#3B82F6'} height="36px" width="36px" />
  }
];

const securityFeatures: Feature[] = [
  {
    title: 'Secure Key Generation',
    description: 'Using industry-standard BIP39 for mnemonic generation and BIP44 for key derivation.',
    icon: <LockIcon color={'#3B82F6'} height="36px" width="36px" />
  },
  {
    title: 'Non-Custodial',
    description: 'Your private keys are never stored on our servers. You have full control of your assets.',
    icon: <ShieldIcon color={'#3B82F6'} height="36px" width="36px" />
  }
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2, // Stagger animation of children
      delayChildren: 0.3,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.6, ease: 'easeOut' } },
};

const iconVariants = {
    hover: { scale: 1.1, transition: { duration: 0.2 } }
};

const buttonVariants = {
    hover: { scale: 1.03, filter: 'brightness(1.1)', transition: { duration: 0.15, ease: "easeOut" } }, 
    tap: { scale: 0.97, filter: 'brightness(0.9)' }
};

const Home: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#050110] text-white">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Canvas
            camera={{ position: [0, 0, 2], fov: 50 }}
            style={{ width: '100%', height: '100%' }}
            onCreated={({ gl }) => {
              gl.setClearColor(new THREE.Color('#050110'));
            }}
          >
            <Suspense fallback={null}>
              <AuroraBackground />
            </Suspense>
            <ambientLight intensity={0.15} />
          </Canvas>
        </div>
        
        <div className="relative z-10 text-center px-4">
          <motion.h1 
            className="text-5xl md:text-7xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-white to-blue-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            Welcome to Nija Wallet
          </motion.h1>
          <motion.p 
            className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Your secure, multi-chain wallet for managing digital assets across Ethereum and Solana networks.
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row justify-center items-center gap-4"
          >
            <Link
              to="/register"
              className="inline-block px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-lg transition-colors duration-200"
            >
              Create Account
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.h2 
            className="text-4xl font-bold text-center mb-12 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Why Choose Nija Wallet?
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                className="bg-gray-900/50 backdrop-blur-lg rounded-xl p-6 border border-gray-700/50"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-blue-400 mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-400">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Security Section */}
      <section className="py-20 px-4 bg-gray-900/30">
        <div className="max-w-7xl mx-auto">
          <motion.h2 
            className="text-4xl font-bold text-center mb-12 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Security First
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {securityFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                className="bg-gray-900/50 backdrop-blur-lg rounded-xl p-6 border border-gray-700/50"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-blue-400 mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-400">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <motion.h2 
            className="text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Ready to Get Started?
          </motion.h2>
          <motion.p 
            className="text-xl text-gray-300 mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Create your Nija Wallet account today and start managing your digital assets securely.
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Link
              to="/register"
              className="inline-block px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-lg transition-colors duration-200"
            >
              Create Account
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900/50 text-gray-400 text-center py-4">
        <p>Powered By Nija</p>
      </footer>
    </div>
  );
};

export default Home;