import React, { useEffect, useState, useCallback, Suspense } from 'react';
import { useNavigate } from 'react-router-dom';
import { ethers } from 'ethers';
import { Keypair, Connection, PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { toast } from 'react-toastify';
import { binanceService } from '../services/binanceService';
import { transactionService } from '../services/TransactionService';
import { API_KEYS, RPC_URLS, EXTERNAL_URLS, API_CONFIG } from '../config/config';
import secureStorage from '../utils/secureStorage';
import AssetCard from '../components/NijaWallet/AssetCard';
import AuroraBackground from '../components/AuroraBackground';
import { Canvas } from '@react-three/fiber';
import * as THREE from 'three';
import Button from '../components/UI/Button';
import Input from '../components/UI/Input';
import ReceivePanel from '../components/ReceivePanel';
import ParentalControlPanel from '../components/ParentalControl/ParentalControlPanel';
import TransactionModal from '../components/TransactionModal';
import { SunIcon, DocumentDuplicateIcon } from '@heroicons/react/24/outline';
import EnhancedTradingChart from '../components/Chart/EnhancedTradingChart';
import TechnicalIndicators from '../components/Chart/TechnicalIndicators';
import OrderbookWidget from '../components/Chart/OrderbookWidget';
import TradeTapeWidget from '../components/Chart/TradeTapeWidget';
import NFTActivityList from '../components/NFTActivityList';
import { initializeNFTActivityWebSocket, processWebSocketMessage, closeNFTActivityWebSocket, testNFTGenWebSocketConnection } from '../utils/nftActivityHandler';
import { useAccountType } from '../contexts/AccountTypeContext';
import ChildWalletInterface from '../components/ChildWalletInterface';

// Update provider types - Attempting to align with potential actual types
interface Eip1193Request {
  method: string;
  params?: any[] | Record<string, any>;
}

interface NijaWalletEthereumProvider extends Eip1193Request {
  isNijaWallet?: boolean;
  name?: string;
  isMetaMask?: boolean;
  request(args: Eip1193Request): Promise<any>;
  on(event: string, callback: (...args: any[]) => void): void;
  removeListener(event: string, callback: (...args: any[]) => void): void;
  connect?(address?: string): Promise<string[]>;
  disconnect?(): Promise<void>;
  isConnected?: boolean;
}

interface NijaSolanaProvider {
  isNijaWallet?: boolean;
  connect(): Promise<{ publicKey: PublicKey }>;
  disconnect(): Promise<void>;
  isConnected?: boolean;
  publicKey?: PublicKey;
  on(event: string, callback: (...args: any[]) => void): void;
  removeListener(event: string, callback: (...args: any[]) => void): void;
}

// Global declaration for window objects - Attempting to fix conflicts
declare global {
  interface Window {
    ethereum?: NijaWalletEthereumProvider; // Use the defined interface and make optional
    nijaWallet?: { // Make nijaWallet optional
      ethereum?: NijaWalletEthereumProvider;
      solana?: NijaSolanaProvider;
    };
    solana?: NijaSolanaProvider; // Use the defined interface and make optional
    solanaWeb3?: any; // Keep as any if its type is complex or unknown
    nijaWalletWs?: WebSocket; // WebSocket connection for NFTGen integration
    nijaWalletHeartbeatInterval?: number | ReturnType<typeof setTimeout>; // Allow ReturnType<typeof setTimeout>
  }
}

export interface ParentalControlSettings {
  enabled: boolean;
  dailyLimit: number;
  allowedAddresses: string[];
  allowedDApps: string[];
  timeRestrictions: {
    start: string;
    end: string;
  };
}

// Default settings if none are found in storage
const defaultPCSettings: ParentalControlSettings = {
  enabled: false,
  dailyLimit: 0,
  allowedAddresses: [],
  allowedDApps: [],
  timeRestrictions: { start: '00:00', end: '23:59' },
};

interface TransactionParams {
  from?: string;
  to: string;
  value?: string | number;
  data?: string;
  gas?: string | number;
  gasPrice?: string | number;
  nonce?: number;
  chainId?: number;
}

interface ChartDataPoint {
  time: number;
  price: number;
}

// Try to find which port NFTGen is running on
const tryNFTGenPort = async (baseIP = API_CONFIG.SERVER_HOST, startPort = 7103, endPort = 7110) => {
  console.log(`Checking for NFTGen on ports ${startPort}-${endPort}...`);

  // Try the primary port first (7103)
  try {
    const response = await fetch(`http://${baseIP}:${startPort}/api/health`, {
      method: 'HEAD',
      mode: 'no-cors',
      // 500ms timeout to avoid long waits
      signal: AbortSignal.timeout(500)
    });
    console.log(`Found NFTGen running on port ${startPort}`);
    return startPort;
  } catch (error) {
    console.log(`NFTGen not responding on primary port ${startPort}, trying alternatives...`);
  }

  // If primary port fails, try alternatives
  for (let port = startPort + 1; port <= endPort; port++) {
    try {
      const response = await fetch(`http://${baseIP}:${port}/api/health`, {
        method: 'HEAD',
        mode: 'no-cors',
        signal: AbortSignal.timeout(500)
      });
      console.log(`Found NFTGen running on port ${port}`);
      return port;
    } catch (error) {
      console.log(`NFTGen not responding on port ${port}`);
    }
  }

  // If all attempts fail, return default port
  console.log(`Could not detect NFTGen on any port, using default ${startPort}`);
  return startPort;
};

export default function NijaWallet() {
  const navigate = useNavigate();
  const { isParent, isChild, userId } = useAccountType();

  // Debug account type detection
  console.log('[NijaWallet] Account type values:', {
    isParent,
    isChild,
    userId
  });

  const [ethAddress, setEthAddress] = useState<string>('');
  const [solAddress, setSolAddress] = useState<string>('');
  const [subEthAddress, setSubEthAddress] = useState<string>('');
  const [subSolAddress, setSubSolAddress] = useState<string>('');
  const [ethPrice, setEthPrice] = useState<number | null>(null);
  const [solPrice, setSolPrice] = useState<number | null>(null);
  const [ethBalance, setEthBalance] = useState<string>('0.00');
  const [solBalance, setSolBalance] = useState<string>('0.00');
  const [subEthBalance, setSubEthBalance] = useState<string>('0.00');
  const [subSolBalance, setSubSolBalance] = useState<string>('0.00');
  const [ethPnlPercent, setEthPnlPercent] = useState<number | null>(null);
  const [solPnlPercent, setSolPnlPercent] = useState<number | null>(null);
  const [subEthPnlPercent, setSubEthPnlPercent] = useState<number | null>(null);
  const [subSolPnlPercent, setSubSolPnlPercent] = useState<number | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<'ETH' | 'SOL'>('ETH');
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1D');
  const [isWalletLoading, setIsWalletLoading] = useState<boolean>(true);
  const [isBalanceLoading, setIsBalanceLoading] = useState<boolean>(false);
  const [isChartLoading, setIsChartLoading] = useState<boolean>(false);
  const [receivePanelVisible, setReceivePanelVisible] = useState<boolean>(false);
  const [transactionModalVisible, setTransactionModalVisible] = useState<boolean>(false);
  const [currentChain, setCurrentChain] = useState<"ETH" | "SOL">("ETH");
  const [currentAddress, setCurrentAddress] = useState<string>('');
  const [isParentalControlEnabled, setIsParentalControlEnabled] = useState<boolean>(false);
  const [parentalControlPanelVisible, setParentalControlPanelVisible] = useState<boolean>(false);
  const [parentalSettings, setParentalSettings] = useState<ParentalControlSettings>(defaultPCSettings);
  const [selectedEthNetwork, setSelectedEthNetwork] = useState<"mainnet" | "sepolia">("sepolia");
  const [selectedSolNetwork, setSelectedSolNetwork] = useState<"mainnet-beta" | "devnet">("devnet");
  const [browserVisible, setBrowserVisible] = useState<boolean>(false);
  const [loggedInEmail, setLoggedInEmail] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<'parent' | 'subAccount'>('parent');
  const [activityLog, setActivityLog] = useState<any[]>([]);
  const [isActivityLoading, setIsActivityLoading] = useState<boolean>(false);
  const [activityLogError, setActivityLogError] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const alchemyApiKey = API_KEYS.ALCHEMY_API_KEY;
  const ethRpcUrl = RPC_URLS.ETH_SEPOLIA;
  const solRpcUrl = RPC_URLS.SOL_DEVNET;
  const parentEthDerivationPath = "m/44'/60'/0'/0/0";
  const subAccountEthDerivationPath = "m/44'/60'/0'/0/1";

  const formatBalance = (balance: string) => parseFloat(balance).toFixed(4);

  const fetchBalances = useCallback(async () => {
    if (!ethAddress || !solAddress) return;
    setIsBalanceLoading(true);
    try {
      const ethProvider = new ethers.JsonRpcProvider(ethRpcUrl);
      const connection = new Connection(solRpcUrl);

      // Always fetch parent balances
      const parentEthBalanceBN = await ethProvider.getBalance(ethAddress);
      setEthBalance(ethers.formatEther(parentEthBalanceBN));
      const parentSolBalanceLamports = await connection.getBalance(new PublicKey(solAddress));
      setSolBalance((parentSolBalanceLamports / LAMPORTS_PER_SOL).toFixed(4));

      // Only fetch sub-account balances if they exist and parental control is enabled
      if (subEthAddress && subSolAddress && isParentalControlEnabled) {
        try {
          const subEthBalanceBN = await ethProvider.getBalance(subEthAddress);
          setSubEthBalance(ethers.formatEther(subEthBalanceBN));
          const subSolBalanceLamports = await connection.getBalance(new PublicKey(subSolAddress));
          setSubSolBalance((subSolBalanceLamports / LAMPORTS_PER_SOL).toFixed(4));
        } catch (subError) {
          console.error("Error fetching sub-account balances:", subError);
          setSubEthBalance('0.0000');
          setSubSolBalance('0.0000');
        }
      } else {
        // Reset sub-account balances if parental control is disabled
        setSubEthBalance('0.0000');
        setSubSolBalance('0.0000');
      }

    } catch (error) {
      console.error("Error fetching balances:", error);
      toast.error("Failed to fetch wallet balances");
      setEthBalance('Error');
      setSolBalance('Error');
      setSubEthBalance('Error');
      setSubSolBalance('Error');
    } finally {
      setIsBalanceLoading(false);
    }
  }, [ethAddress, solAddress, subEthAddress, subSolAddress, ethRpcUrl, solRpcUrl, isParentalControlEnabled]);

  const fetchLivePrices = useCallback(async () => {
     try {
        const [ethTickerData, solTickerData] = await Promise.all([
          binanceService.getTickerPrice('ETHUSDT'),
          binanceService.getTickerPrice('SOLUSDT')
        ]);
        setEthPrice(parseFloat(ethTickerData.price));
        setSolPrice(parseFloat(solTickerData.price));

        setEthPnlPercent(Math.random() > 0.5 ? 1.23 : -0.45);
        setSolPnlPercent(Math.random() > 0.5 ? 2.50 : -1.10);

        setSubEthPnlPercent(Math.random() > 0.5 ? 0.75 : -0.25);
        setSubSolPnlPercent(Math.random() > 0.5 ? 1.80 : -0.60);

      } catch (error) {
        console.error("Error fetching latest prices:", error);
        setEthPrice(null);
        setSolPrice(null);
        setEthPnlPercent(null);
        setSolPnlPercent(null);
        setSubEthPnlPercent(null);
        setSubSolPnlPercent(null);
     }
  }, []);

  const fetchChartData = useCallback(async (asset: 'ETH' | 'SOL', timeframe: string) => {
    setIsChartLoading(true);
    setChartData([]);
    const symbol = asset === 'ETH' ? 'ETHUSDT' : 'SOLUSDT';
    const intervalMap: { [key: string]: string } = {
        '1H': '1h',
        '1D': '1d',
        '7D': '1w',
        '1M': '1M',
        '1Y': '1M'
    };
    const interval = intervalMap[timeframe] || '1d';

    try {
        const klines = await binanceService.getKlines(symbol, interval);
        const formattedData: ChartDataPoint[] = klines.map((k: any) => ({
            time: k.openTime,
            price: parseFloat(k.close)
        }));
        setChartData(formattedData);
    } catch (error) {
        console.error(`Error fetching chart data for ${symbol} (${interval}):`, error);
        toast.error(`Failed to load ${asset} chart data`);
        setChartData([]);
    } finally {
        setIsChartLoading(false);
    }
  }, []);

  const fetchActivityLog = useCallback(async () => {
    if ((currentView === 'parent' && (!ethAddress || !solAddress)) ||
        (currentView === 'subAccount' && (!subEthAddress || !subSolAddress))) {
      console.log("Addresses not yet available for activity log fetch.");
      return;
    }

    setIsActivityLoading(true);
    setActivityLog([]);
    setActivityLogError(false);

    const network = selectedAsset;
    const addressToFetch = currentView === 'parent'
      ? (network === 'ETH' ? ethAddress : solAddress)
      : (network === 'ETH' ? subEthAddress : subSolAddress);

    console.log(`Fetching ${network} activity log for ${currentView} view: ${addressToFetch}`);

    if (!addressToFetch) {
        console.warn(`No address found for ${network} in ${currentView} view.`);
        setIsActivityLoading(false);
        return;
    }

    try {
      const history = await transactionService.getTransactionHistory(addressToFetch, network);
      console.log(`Fetched ${network} history:`, history);
      setActivityLog(history || []);
    } catch (error) {
      console.error(`Error fetching ${network} activity log:`, error);
      toast.error(`Failed to fetch ${network} activity log`);
      setActivityLog([]);
      setActivityLogError(true);
    } finally {
      setIsActivityLoading(false);
    }
  }, [currentView, ethAddress, solAddress, subEthAddress, subSolAddress, selectedAsset]);

  useEffect(() => {
    const loadUserData = async () => {
      setIsWalletLoading(true);
      let userEmail = null;
      try {
        // Get logged in user from session storage
        const loggedInUser = sessionStorage.getItem('loggedInUser');
        const accountType = sessionStorage.getItem('accountType');

        console.log('[NijaWallet] Session storage debug:', {
          loggedInUser,
          accountType,
          parsedLoggedInUser: loggedInUser ? JSON.parse(loggedInUser) : null,
          parsedAccountType: accountType ? JSON.parse(accountType) : null
        });

        if (!loggedInUser) {
          console.warn("No logged in user found in session storage, redirecting to login.");
          navigate('/login');
          return;
        }
        const sessionUserData = JSON.parse(loggedInUser);
        const { email } = sessionUserData;
        userEmail = email;
        setLoggedInEmail(email);

        // Debug: Check if user data has isParent field
        console.log('[NijaWallet] User data analysis:', {
          hasIsParentField: 'isParent' in sessionUserData,
          isParentValue: sessionUserData.isParent,
          isParentType: typeof sessionUserData.isParent,
          fullUserData: sessionUserData
        });

        // Try to get user data from secure storage first
        let userData = null;
        try {
          userData = await secureStorage.get(email);
        } catch (error) {
          console.warn("Error accessing secure storage:", error);
        }

        // If not found in secure storage, try localStorage as fallback
        if (!userData) {
          const userDataString = localStorage.getItem(email);
          if (!userDataString) {
            throw new Error(`User data not found for ${email}`);
          }
          userData = JSON.parse(userDataString);

          // Migrate to secure storage for future use
          try {
            await secureStorage.set(email, userData);
            console.log("Migrated user data to secure storage");
          } catch (error) {
            console.warn("Failed to migrate user data to secure storage:", error);
          }
        }

        const { mnemonic } = userData;
        if (!mnemonic) {
          throw new Error(`Mnemonic not found for ${email}`);
        }

        const parentEthWallet = ethers.HDNodeWallet.fromPhrase(mnemonic, undefined, parentEthDerivationPath);
        const parentEthAddr = parentEthWallet.address;
        const seedBytes = ethers.getBytes(ethers.Mnemonic.fromPhrase(mnemonic).computeSeed());
        const parentSolKp = Keypair.fromSeed(seedBytes.slice(0, 32));
        const parentSolAddr = parentSolKp.publicKey.toBase58();

        // Check if sub-account addresses already exist in storage
        let subEthAddr = '';
        let subSolAddr = '';

        try {
          // Try to get existing sub-account addresses from secure storage
          const existingSubEth = await secureStorage.get(`sub_eth_address_${email}`);
          const existingSubSol = await secureStorage.get(`sub_sol_address_${email}`);

          if (existingSubEth && existingSubSol) {
            subEthAddr = existingSubEth;
            subSolAddr = existingSubSol;
            console.log("Loaded existing sub-account addresses from secure storage");
          } else {
            // Fallback to localStorage
            const localSubEth = localStorage.getItem(`sub_eth_address_${email}`);
            const localSubSol = localStorage.getItem(`sub_sol_address_${email}`);

            if (localSubEth && localSubSol) {
              subEthAddr = localSubEth;
              subSolAddr = localSubSol;
              console.log("Loaded existing sub-account addresses from localStorage");

              // Migrate to secure storage
              await secureStorage.set(`sub_eth_address_${email}`, subEthAddr);
              await secureStorage.set(`sub_sol_address_${email}`, subSolAddr);
            } else {
              // Generate new sub-account addresses
              const subEthWallet = ethers.HDNodeWallet.fromPhrase(mnemonic, undefined, subAccountEthDerivationPath);
              subEthAddr = subEthWallet.address;
              const subSolKp = Keypair.fromSeed(seedBytes.slice(32, 64));
              subSolAddr = subSolKp.publicKey.toBase58();
              console.log("Generated new sub-account addresses");
            }
          }
        } catch (error) {
          console.warn("Error loading sub-account addresses, generating new ones:", error);
          // Generate new sub-account addresses as fallback
          const subEthWallet = ethers.HDNodeWallet.fromPhrase(mnemonic, undefined, subAccountEthDerivationPath);
          subEthAddr = subEthWallet.address;
          const subSolKp = Keypair.fromSeed(seedBytes.slice(32, 64));
          subSolAddr = subSolKp.publicKey.toBase58();
        }

        setEthAddress(parentEthAddr);
        setSolAddress(parentSolAddr);
        setSubEthAddress(subEthAddr);
        setSubSolAddress(subSolAddr);
        console.log("Wallet initialized:", {
            parentEth: parentEthAddr, parentSol: parentSolAddr,
            subEth: subEthAddr, subSol: subSolAddr
        });

        // Try to get parental control settings from secure storage first
        let pcEnabled = false;
        let pcSettings = defaultPCSettings;

        try {
          const securepcEnabled = await secureStorage.get(`pc_enabled_${email}`);
          if (securepcEnabled !== null) {
            pcEnabled = securepcEnabled;
          } else {
            // Fallback to localStorage
            const pcEnabledString = localStorage.getItem(`pc_enabled_${email}`);
            pcEnabled = pcEnabledString ? JSON.parse(pcEnabledString) : false;

            // Migrate to secure storage
            await secureStorage.set(`pc_enabled_${email}`, pcEnabled);
          }

          const securePcSettings = await secureStorage.get(`pc_settings_${email}`);
          if (securePcSettings !== null) {
            pcSettings = securePcSettings;
          } else {
            // Fallback to localStorage
            const pcSettingsString = localStorage.getItem(`pc_settings_${email}`);
            pcSettings = pcSettingsString ? JSON.parse(pcSettingsString) : defaultPCSettings;

            // Migrate to secure storage
            await secureStorage.set(`pc_settings_${email}`, pcSettings);
          }
        } catch (error) {
          console.warn("Error loading parental control settings from secure storage, using localStorage:", error);
          // Fallback to localStorage
          const pcEnabledString = localStorage.getItem(`pc_enabled_${email}`);
          pcEnabled = pcEnabledString ? JSON.parse(pcEnabledString) : false;

          const pcSettingsString = localStorage.getItem(`pc_settings_${email}`);
          pcSettings = pcSettingsString ? JSON.parse(pcSettingsString) : defaultPCSettings;
        }

        setIsParentalControlEnabled(pcEnabled);
        setParentalSettings(pcSettings);

        console.log("Parental Control Loaded:", { enabled: pcEnabled, settings: pcSettings });
      } catch (error) {
        console.error("Error initializing wallet:", error);
        toast.error("Failed to initialize wallet. Redirecting to login.");
        sessionStorage.removeItem('loggedInUser');
        if (userEmail) {
          try {
            await secureStorage.remove(`pc_enabled_${userEmail}`);
            await secureStorage.remove(`pc_settings_${userEmail}`);
          } catch (e) {
            console.warn("Error removing secure storage items:", e);
          }
          localStorage.removeItem(`pc_enabled_${userEmail}`);
          localStorage.removeItem(`pc_settings_${userEmail}`);
        }
        navigate('/login');
      } finally {
        setIsWalletLoading(false);
      }
    };

    loadUserData();
  }, [navigate]);

  useEffect(() => {
    if (ethAddress && solAddress) {
      fetchBalances();
      fetchLivePrices();
      fetchChartData(selectedAsset, selectedTimeframe);
      fetchActivityLog();

      // Initialize WebSocket connection for NFT activities if not already connected
      if (!window.nijaWalletWs || window.nijaWalletWs.readyState !== WebSocket.OPEN) {
        try {
          // Use the correct WebSocket URL with proper port (Nwallet WebSocket runs on port 6103)
          const wsUrl = `ws://${API_CONFIG.SERVER_HOST}:6103/ws`;
          console.log(`Initializing WebSocket connection to ${wsUrl} for NFT activities`);

          // Store the Ethereum address in localStorage to ensure it's available for WebSocket
          if (ethAddress) {
            try {
              localStorage.setItem('eth_address', ethAddress);
              localStorage.setItem('nija_eth_address', ethAddress);
              console.log('Stored Ethereum address in localStorage for WebSocket:', ethAddress);
            } catch (e) {
              console.warn('Error storing Ethereum address in localStorage:', e);
            }
          }

          // Close any existing connection first
          if (window.nijaWalletWs) {
            try {
              closeNFTActivityWebSocket(window.nijaWalletWs);
            } catch (e) {
              console.warn('Error closing existing WebSocket connection:', e);
            }
          }

          // Initialize new WebSocket connection with a small delay to ensure localStorage is updated
          setTimeout(() => {
            window.nijaWalletWs = initializeNFTActivityWebSocket(wsUrl);
          }, 100);

          if (window.nijaWalletWs) {
            // Register for NFT activities when connection is established
            window.nijaWalletWs.addEventListener('open', () => {
              window.nijaWalletWs?.send(JSON.stringify({
                type: 'subscribe',
                channel: 'nft_activities',
                data: {
                  address: ethAddress
                }
              }));
            });
          }
        } catch (error) {
          console.error('Error initializing WebSocket connection for NFT activities:', error);
        }
      }
    }

    // Clean up WebSocket connection on component unmount
    return () => {
      if (window.nijaWalletWs) {
        try {
          window.nijaWalletWs.close();
        } catch (error) {
          console.error('Error closing WebSocket connection:', error);
        }
      }
    };
  }, [ethAddress, solAddress, subEthAddress, subSolAddress, fetchBalances, fetchLivePrices, fetchChartData, fetchActivityLog, selectedAsset, selectedTimeframe, isParentalControlEnabled]);

  useEffect(() => {
    if (ethAddress || solAddress || subEthAddress || subSolAddress) {
        fetchActivityLog();
    }
  }, [currentView, selectedAsset, fetchActivityLog, ethAddress, solAddress, subEthAddress, subSolAddress]);

  const handleTimeframeChange = (newTimeframe: string) => {
       setSelectedTimeframe(newTimeframe);
   };

   const handleAssetSelect = (asset: 'ETH' | 'SOL') => {
       setSelectedAsset(asset);
   };

  const copyToClipboard = (text: string, label: string) => {
    try {
      // Always use the fallback method since navigator.clipboard might be undefined
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        const successful = document.execCommand('copy');
        if (successful) {
          toast.success(`${label} address copied to clipboard!`);
        } else {
          toast.error('Failed to copy address');
        }
      } catch (err) {
        console.error('Failed to copy text: ', err);
        toast.error('Failed to copy address');
      } finally {
        textArea.remove();
      }
    } catch (err) {
      console.error('Failed to copy text: ', err);
      toast.error('Failed to copy address');
    }
  };

  const toggleParentalControl = () => {
      setParentalControlPanelVisible(true);
  };

  const handlePCSave = async (settings: ParentalControlSettings) => {
    if (!loggedInEmail) {
      toast.error("Cannot save settings: User email not found.");
      return;
    }
    try {
      // Save to secure storage
      await secureStorage.set(`pc_settings_${loggedInEmail}`, settings);
      await secureStorage.set(`pc_enabled_${loggedInEmail}`, settings.enabled);

      // Also save to localStorage as fallback
      localStorage.setItem(`pc_settings_${loggedInEmail}`, JSON.stringify(settings));
      localStorage.setItem(`pc_enabled_${loggedInEmail}`, JSON.stringify(settings.enabled));

      setParentalSettings(settings);
      setIsParentalControlEnabled(settings.enabled);

      // If parental control is disabled, force view to parent account
      if (!settings.enabled) {
        setCurrentView('parent');
      }

      toast.success("Parental control settings saved!");
      console.log("Parental Control Saved:", { enabled: settings.enabled, settings });

    } catch (error) {
      console.error("Error saving parental control settings:", error);
      toast.error("Failed to save parental control settings.");
    }
  };

  // Generate sub-account function that creates actual wallet addresses
  const generateSubAccount = async () => {
    if (!loggedInEmail) {
      toast.error("Cannot generate sub-account: User email not found.");
      return;
    }

    try {
      setIsLoading(true);

      // Get the user's main mnemonic
      let userData = null;
      try {
        userData = await secureStorage.get(loggedInEmail);
      } catch (error) {
        // Fallback to localStorage
        const userDataString = localStorage.getItem(loggedInEmail);
        if (userDataString) {
          userData = JSON.parse(userDataString);
        }
      }

      if (!userData || !userData.mnemonic) {
        throw new Error("User mnemonic not found");
      }

      const { mnemonic } = userData;

      // Generate sub-account addresses using the same mnemonic but different derivation paths
      const subEthWallet = ethers.HDNodeWallet.fromPhrase(mnemonic, undefined, subAccountEthDerivationPath);
      const newSubEthAddr = subEthWallet.address;

      const seedBytes = ethers.getBytes(ethers.Mnemonic.fromPhrase(mnemonic).computeSeed());
      const subSolKp = Keypair.fromSeed(seedBytes.slice(32, 64));
      const newSubSolAddr = subSolKp.publicKey.toBase58();

      // Update the state with new sub-account addresses
      setSubEthAddress(newSubEthAddr);
      setSubSolAddress(newSubSolAddr);

      // Store sub-account addresses in secure storage
      try {
        await secureStorage.set(`sub_eth_address_${loggedInEmail}`, newSubEthAddr);
        await secureStorage.set(`sub_sol_address_${loggedInEmail}`, newSubSolAddr);
        await secureStorage.set(`sub_account_generated_${loggedInEmail}`, true);
      } catch (error) {
        // Fallback to localStorage
        localStorage.setItem(`sub_eth_address_${loggedInEmail}`, newSubEthAddr);
        localStorage.setItem(`sub_sol_address_${loggedInEmail}`, newSubSolAddr);
        localStorage.setItem(`sub_account_generated_${loggedInEmail}`, 'true');
      }

      // Generate credentials for the sub-account
      const generateUsername = () => {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let username = 'sub_';
        for (let i = 0; i < 8; i++) {
          username += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return username;
      };

      const generatePassword = () => {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_-+=';
        let password = '';
        for (let i = 0; i < 12; i++) {
          password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return password;
      };

      const credentials = {
        username: generateUsername(),
        password: generatePassword(),
        ethAddress: newSubEthAddr,
        solAddress: newSubSolAddr,
        createdAt: new Date().toISOString()
      };

      // Store credentials securely
      try {
        await secureStorage.set(`sub_account_credentials_${loggedInEmail}`, credentials);
      } catch (error) {
        // Fallback to localStorage with encryption
        const encryptedCredentials = btoa(JSON.stringify(credentials));
        localStorage.setItem(`sub_account_credentials_${loggedInEmail}`, encryptedCredentials);
      }

      // Fetch balances for the new sub-account
      await fetchBalances();

      toast.success(`Sub-account generated successfully!

ETH Address: ${newSubEthAddr.slice(0, 10)}...
SOL Address: ${newSubSolAddr.slice(0, 10)}...
Username: ${credentials.username}
Password: ${credentials.password}

Please save these credentials securely!`);

      console.log("Sub-account generated:", {
        ethAddress: newSubEthAddr,
        solAddress: newSubSolAddr,
        credentials
      });

    } catch (error) {
      console.error("Error generating sub-account:", error);
      toast.error("Failed to generate sub-account. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const openTransactionModal = (chain: "ETH" | "SOL") => {
    setCurrentChain(chain);
    setTransactionModalVisible(true);
  };

  const openReceivePanel = (chain: "ETH" | "SOL", address: string) => {
    setCurrentChain(chain);
    setCurrentAddress(address);
    setReceivePanelVisible(true);
  };

  const handleReceive = (type: 'eth' | 'sol') => {
    const address = type === 'eth' ? ethAddress : solAddress;
    if (address) {
      navigator.clipboard.writeText(address);
      toast.success('Address copied to clipboard!');
    }
  };

  const handleSend = (type: 'eth' | 'sol') => {
    toast.info('Send functionality coming soon!');
  };

  const handleGetTestTokens = async (type: 'eth' | 'sol') => {
    try {
      if (type === 'eth') {
        window.open('https://cloud.google.com/application/web3/faucet/ethereum/sepolia', '_blank');
      } else {
        window.open('https://faucet.solana.com/', '_blank');
      }
    } catch (error) {
      console.error('Error getting test tokens:', error);
      toast.error('Failed to get test tokens. Please try again.');
    }
  };

  const handleOpenNFTGen = async () => {
    console.log('Opening NFTGen...');

    if (!ethAddress) {
      toast.error('Ethereum address not available. Please wait for wallet to initialize.');
      return;
    }

    try {
      // Create a unique session ID with more entropy
      const sessionId = `nija_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      // Generate a more secure JWT-like token for session validation
      // This is a simplified version - in production, use a proper JWT library
      const generateToken = (payload: any, expiresInMinutes = 60) => {
        const header = { alg: 'HS256', typ: 'JWT' };
        const now = Math.floor(Date.now() / 1000);
        const expiresAt = now + (expiresInMinutes * 60);

        const tokenPayload = {
          ...payload,
          iat: now,
          exp: expiresAt,
          jti: `${Math.random().toString(36).substring(2, 15)}_${Math.random().toString(36).substring(2, 15)}` // Add unique JWT ID
        };

        // Create a more secure signature by combining multiple values
        const signatureBase = `${JSON.stringify(header)}.${JSON.stringify(tokenPayload)}.${now}.${sessionId}.${ethAddress}`;
        const signature = btoa(signatureBase);

        // In a real implementation, we would use a proper HMAC signature
        const tokenParts = [
          btoa(JSON.stringify(header)),
          btoa(JSON.stringify(tokenPayload)),
          signature
        ];

        return tokenParts.join('.');
      };

      // Create comprehensive session data with enhanced security
      const sessionData = {
        sessionId,
        address: ethAddress,
        chainId: '0xaa36a7', // Sepolia testnet
        nonce: Date.now(),
        timestamp: Date.now(),
        expiresAt: Date.now() + (60 * 60 * 1000), // 1 hour expiry
        source: 'nija_wallet',
        origin: window.location.origin,
        version: '1.1.0', // Add version for compatibility checks
        capabilities: ['eip1193', 'signMessage', 'signTransaction', 'nftSync'], // Declare supported capabilities
        token: generateToken({
          sessionId,
          address: ethAddress,
          chainId: '0xaa36a7',
          origin: window.location.origin
        })
      };

      // Store session in localStorage for NFTGen to access
      localStorage.setItem('nija_wallet_session', JSON.stringify(sessionData));
      // Use the new standardized key as well
      localStorage.setItem('nwallet_session', JSON.stringify(sessionData));

      // Also store individual components for backward compatibility
      localStorage.setItem('nija_session_id', sessionId);
      localStorage.setItem('nija_eth_address', ethAddress);
      localStorage.setItem('nija_chain_id', '0xaa36a7');
      localStorage.setItem('nija_wallet_connection_time', Date.now().toString());

      // Set up WebSocket connection for activity synchronization with improved security
      try {
        // Use the correct WebSocket URL with proper port (Nwallet WebSocket runs on port 6103)
        const wsUrl = `ws://${API_CONFIG.SERVER_HOST}:6103/ws`;
        console.log(`Setting up WebSocket connection to ${wsUrl}`);

        // Close any existing connection properly
        if (window.nijaWalletWs) {
          try {
            closeNFTActivityWebSocket(window.nijaWalletWs);
          } catch (e) {
            console.warn('Error closing existing WebSocket connection:', e);
          }
        }

        // Create new WebSocket connection using our activity handler with a small delay
        // to ensure localStorage is updated with the Ethereum address
        setTimeout(() => {
          // Store the Ethereum address in localStorage to ensure it's available for WebSocket
          try {
            localStorage.setItem('eth_address', ethAddress);
            localStorage.setItem('nija_eth_address', ethAddress);
            console.log('Stored Ethereum address in localStorage for WebSocket:', ethAddress);
          } catch (e) {
            console.warn('Error storing Ethereum address in localStorage:', e);
          }

          // Initialize WebSocket and store the reference
          window.nijaWalletWs = initializeNFTActivityWebSocket(wsUrl);

          // Set up event listeners after WebSocket is created
          if (!window.nijaWalletWs) {
            console.warn('WebSocket not available for setting up listeners');
            return;
          }

          // Send session registration message when connection is established
          window.nijaWalletWs.addEventListener('open', () => {
            // Send authentication message with token
            window.nijaWalletWs?.send(JSON.stringify({
              type: 'authenticate',
              data: {
                token: sessionData.token,
                sessionId,
                address: ethAddress,
                chainId: '0xaa36a7',
                timestamp: Date.now(),
                version: '1.1.0'
              }
            }));

            // Register for NFT activities after authentication
            window.nijaWalletWs?.send(JSON.stringify({
              type: 'subscribe',
              channel: 'nft_activities',
              data: {
                address: ethAddress,
                sessionId,
                timestamp: Date.now()
              }
            }));

            // Set up heartbeat to keep connection alive
            const heartbeatInterval = setInterval(() => {
              if (window.nijaWalletWs?.readyState === WebSocket.OPEN) {
                window.nijaWalletWs.send(JSON.stringify({
                  type: 'heartbeat',
                  timestamp: Date.now(),
                  sessionId,
                  address: ethAddress // Include address in heartbeat for better security
                }));
              } else {
                clearInterval(heartbeatInterval);
              }
            }, 30000); // 30 second heartbeat

            // Store the interval ID to clear it later if needed
            window.nijaWalletHeartbeatInterval = heartbeatInterval;
          });

          // Handle connection close
          window.nijaWalletWs.addEventListener('close', (event) => {
            if (window.nijaWalletHeartbeatInterval) {
              clearInterval(window.nijaWalletHeartbeatInterval);
            }
            console.log(`WebSocket connection closed: ${event.code} - ${event.reason || 'No reason provided'}`);

            // Attempt to reconnect if closed unexpectedly
            if (event.code !== 1000 && event.code !== 1001) {
              console.log('Attempting to reconnect WebSocket...');
              setTimeout(() => {
                try {
                  window.nijaWalletWs = initializeNFTActivityWebSocket(wsUrl);
                } catch (reconnectError) {
                  console.error('Failed to reconnect WebSocket:', reconnectError);
                }
              }, 5000); // Wait 5 seconds before reconnecting
            }
          });

          // Handle errors
          window.nijaWalletWs.addEventListener('error', (error) => {
            console.error('WebSocket error:', error);
          });
        }, 100);
      } catch (wsError) {
        console.error('Failed to set up WebSocket connection:', wsError);
        // Continue anyway - WebSocket is not critical for basic functionality
      }

      // Encode session for URL parameters with the secure token
      const encodedSession = encodeURIComponent(JSON.stringify({
        sessionId,
        address: ethAddress,
        chainId: '0xaa36a7',
        token: sessionData.token,
        timestamp: sessionData.timestamp,
        version: '1.1.0'
      }));

      // Use the fixed NFTGen URL with secure session
      const nftGenUrl = `http://${API_CONFIG.SERVER_HOST}:7103/?session=${encodedSession}`;

      console.log('Opening NFTGen at URL:', nftGenUrl);
      toast.success('Connecting to NFTGen with your Sepolia wallet');

      // Also store the session in the NFTGen localStorage directly with improved security
      try {
        // Create a hidden iframe to access the NFTGen localStorage
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = `http://${API_CONFIG.SERVER_HOST}:7103/`;

        // When the iframe loads, store the session in its localStorage
        iframe.onload = () => {
          try {
            // @ts-ignore
            const iframeWindow = iframe.contentWindow;
            if (iframeWindow) {
              // Store with both keys to ensure compatibility
              iframeWindow.localStorage.setItem('nija_wallet_session', JSON.stringify(sessionData));
              iframeWindow.localStorage.setItem('nwallet_session', JSON.stringify(sessionData));
              console.log('Session stored in NFTGen localStorage via iframe with both keys');

              // Also verify the session was stored correctly
              const storedSession = iframeWindow.localStorage.getItem('nija_wallet_session');
              if (storedSession) {
                console.log('Verified session was stored correctly in NFTGen localStorage');
              } else {
                console.warn('Failed to verify session was stored in NFTGen localStorage');
              }
            }
            // Remove the iframe after storing the session
            document.body.removeChild(iframe);
          } catch (iframeError) {
            console.error('Error storing session in iframe:', iframeError);
            // Remove the iframe even if there's an error
            document.body.removeChild(iframe);
          }
        };

        // Add the iframe to the document
        document.body.appendChild(iframe);
      } catch (iframeError) {
        console.error('Error creating iframe for session storage:', iframeError);
        // Continue anyway - this is just a fallback
      }

      // Open NFTGen in a new tab
      window.open(nftGenUrl, '_blank');
    } catch (error) {
      console.error('Error connecting to NFTGen:', error);
      toast.error('Failed to connect to NFTGen. Please try again.');
    }
  };

  const handleBrowserClick = () => {
    window.open(EXTERNAL_URLS.BROWSER_URL, '_blank');
    setBrowserVisible(false);
  };

  const handleTransactionComplete = async (recipient: string, amount: string) => {
    console.log(`Placeholder: Transaction completed to ${recipient} for ${amount} ${selectedAsset}`);
    toast.success("Transaction Recorded (Placeholder)");
    setTimeout(fetchBalances, 2000);
  };

  // Add network switching handlers
  const handleEthNetworkChange = (network: "mainnet" | "sepolia") => {
    setSelectedEthNetwork(network);
    toast.info(`Switched to Ethereum ${network}`);
    // Here you would typically update your provider connection
    // And possibly refresh balances for the new network
  };

  const handleSolNetworkChange = (network: "mainnet-beta" | "devnet") => {
    setSelectedSolNetwork(network);
    toast.info(`Switched to Solana ${network}`);
    // Here you would typically update your Solana connection
    // And possibly refresh balances for the new network
  };

  // Add an effect to ensure parent view is forced when parental control is disabled
  useEffect(() => {
    if (!isParentalControlEnabled && currentView === 'subAccount') {
      setCurrentView('parent');
    }
  }, [isParentalControlEnabled, currentView]);

  if (isWalletLoading) {
    return (
      <div className="w-screen h-screen flex items-center justify-center bg-[#0A0F1A] text-gray-400">
        Loading Nija Wallet...
      </div>
    );
  }

  const displayEthAddress = currentView === 'parent' ? ethAddress : subEthAddress;
  const displaySolAddress = currentView === 'parent' ? solAddress : subSolAddress;
  const displayEthBalance = currentView === 'parent' ? ethBalance : subEthBalance;
  const displaySolBalance = currentView === 'parent' ? solBalance : subSolBalance;
  const displayEthPnlPercent = currentView === 'parent' ? ethPnlPercent : subEthPnlPercent;
  const displaySolPnlPercent = currentView === 'parent' ? solPnlPercent : subSolPnlPercent;

  const displayEthBalanceNum = parseFloat(displayEthBalance);
  const displaySolBalanceNum = parseFloat(displaySolBalance);
  const calculatedEthPnlValue = (displayEthPnlPercent !== null && ethPrice !== null && !isNaN(displayEthBalanceNum))
    ? (displayEthBalanceNum * ethPrice * displayEthPnlPercent / 100)
    : null;
  const calculatedSolPnlValue = (displaySolPnlPercent !== null && solPrice !== null && !isNaN(displaySolBalanceNum))
    ? (displaySolBalanceNum * solPrice * displaySolPnlPercent / 100)
    : null;

  return (
    <div className="relative min-h-screen w-full bg-black text-white overflow-hidden">
      {/* Background Canvas */}
      <Suspense fallback={<div className="absolute inset-0 bg-black flex items-center justify-center text-white">Loading 3D Background...</div>}>
        <Canvas className="absolute inset-0 z-0">
          <AuroraBackground />
        </Canvas>
      </Suspense>

      {/* Content Area */}
      <div className="relative z-10 p-4 md:p-8">
        {/* Header Section (No more <h1>) */}
        <div className="flex justify-between items-center mb-4">
          {/* Network Selection */}
          <div className="w-1/3 flex space-x-2">
            <select
              value={selectedEthNetwork}
              onChange={(e) => handleEthNetworkChange(e.target.value as "mainnet" | "sepolia")}
              className="bg-gray-900 bg-opacity-70 text-white border border-gray-700 rounded px-2 py-1 text-sm"
            >
              <option value="sepolia">ETH Sepolia</option>
              <option value="mainnet">ETH Mainnet</option>
            </select>
            <select
              value={selectedSolNetwork}
              onChange={(e) => handleSolNetworkChange(e.target.value as "mainnet-beta" | "devnet")}
              className="bg-gray-900 bg-opacity-70 text-white border border-gray-700 rounded px-2 py-1 text-sm"
            >
              <option value="devnet">SOL Devnet</option>
              <option value="mainnet-beta">SOL Mainnet</option>
            </select>
          </div>

          {/* View Toggle Buttons - Only show if parental control is enabled */}
          <div className="flex justify-center space-x-2 w-1/3">
            {isParentalControlEnabled && (
              <>
                <Button
                  onClick={() => setCurrentView('parent')}
                  variant={currentView === 'parent' ? 'primary' : 'secondary'}
                  className="px-3 py-1 text-sm"
                >
                  Parent View
                </Button>
                <Button
                  onClick={() => setCurrentView('subAccount')}
                  variant={currentView === 'subAccount' ? 'primary' : 'secondary'}
                  className="px-3 py-1 text-sm"
                >
                  Sub-Account View
                </Button>
              </>
            )}
          </div>

          {/* Parental Controls Button - Always show, but different text based on state */}
          <div className="flex justify-end items-center space-x-4 w-1/3">
            <Button
              variant="ghost"
              onClick={() => setParentalControlPanelVisible(true)}
              className={`${isParentalControlEnabled ? 'text-green-400 hover:text-green-300' : 'text-purple-400 hover:text-purple-300'}`}
            >
              {isParentalControlEnabled ? 'Parental Controls' : 'Enable Parental Controls'}
            </Button>
          </div>
        </div>

        {/* Address Display Card */}
        <div className="bg-gray-900 bg-opacity-70 backdrop-blur-sm rounded-lg p-4 md:p-6 mb-8 border border-gray-700/50">
          <h2 className="text-xl font-semibold mb-4 text-purple-300">Wallet Addresses</h2>

          {/* Parent Section */}
          <div className="mb-4 pb-4 border-b border-gray-700">
            <h3 className="text-md font-medium text-gray-200 mb-2">Parent Account</h3>
            <div className="space-y-2 text-sm">
              {/* Parent ETH Row */}
              <div className="flex items-center justify-between">
                <span className="text-gray-400 w-10">ETH:</span>
                <div className="flex items-center space-x-2 flex-grow min-w-0">
                  <span className="font-mono text-gray-200 truncate flex-shrink min-w-0" title={ethAddress}>{ethAddress}</span>
                  <button onClick={() => copyToClipboard(ethAddress, 'Parent ETH')} className="text-gray-400 hover:text-white p-1 rounded hover:bg-gray-700 transition duration-150 flex-shrink-0">
                      <DocumentDuplicateIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
              {/* Parent SOL Row */}
              <div className="flex items-center justify-between">
                <span className="text-gray-400 w-10">SOL:</span>
                <div className="flex items-center space-x-2 flex-grow min-w-0">
                  <span className="font-mono text-gray-200 truncate flex-shrink min-w-0" title={solAddress}>{solAddress}</span>
                  <button onClick={() => copyToClipboard(solAddress, 'Parent SOL')} className="text-gray-400 hover:text-white p-1 rounded hover:bg-gray-700 transition duration-150 flex-shrink-0">
                      <DocumentDuplicateIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Sub-Account Section - Only show when parental control is enabled */}
          {isParentalControlEnabled && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-md font-medium text-gray-200">Sub-Account</h3>
                <Button
                  variant="web3outline"
                  onClick={generateSubAccount}
                  disabled={isLoading}
                  className="text-xs py-1 px-2"
                >
                  {isLoading ? 'Generating...' : 'Generate New'}
                </Button>
              </div>
              <div className="space-y-2 text-sm">
                {/* Sub ETH Row */}
                <div className="flex items-center justify-between">
                  <span className="text-gray-400 w-10">ETH:</span>
                  <div className="flex items-center space-x-2 flex-grow min-w-0">
                    <span className="font-mono text-gray-200 truncate flex-shrink min-w-0" title={subEthAddress}>{subEthAddress}</span>
                    <button onClick={() => copyToClipboard(subEthAddress, 'Sub ETH')} className="text-gray-400 hover:text-white p-1 rounded hover:bg-gray-700 transition duration-150 flex-shrink-0">
                        <DocumentDuplicateIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                {/* Sub SOL Row */}
                <div className="flex items-center justify-between">
                  <span className="text-gray-400 w-10">SOL:</span>
                  <div className="flex items-center space-x-2 flex-grow min-w-0">
                    <span className="font-mono text-gray-200 truncate flex-shrink min-w-0" title={subSolAddress}>{subSolAddress}</span>
                    <button onClick={() => copyToClipboard(subSolAddress, 'Sub SOL')} className="text-gray-400 hover:text-white p-1 rounded hover:bg-gray-700 transition duration-150 flex-shrink-0">
                        <DocumentDuplicateIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Loading Indicator for Wallet Initialization */}
        {isWalletLoading && (
          <div className="text-center py-10">Loading Wallet...</div>
        )}

        {!isWalletLoading && (
          <>
            {/* Child Wallet Interface - Only show for child accounts */}
            {isChild && (
              <div className="mb-8">
                <ChildWalletInterface
                  onTransactionRequest={(toAddress, amount, network) => {
                    console.log('Child transaction request:', { toAddress, amount, network });
                    // Handle the transaction request here
                  }}
                />
              </div>
            )}

            {/* Asset Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <AssetCard
                icon="logos:ethereum"
                name="Ethereum"
                symbol="ETH"
                balance={currentView === 'parent' ? formatBalance(ethBalance) : formatBalance(subEthBalance)}
                price={ethPrice}
                pnlPercent={currentView === 'parent' ? ethPnlPercent : subEthPnlPercent}
                pnlValue={
                    (currentView === 'parent' && ethPrice && ethPnlPercent !== null && !isNaN(parseFloat(ethBalance)))
                    ? parseFloat(ethBalance) * ethPrice * (ethPnlPercent / 100)
                    : (currentView === 'subAccount' && ethPrice && subEthPnlPercent !== null && !isNaN(parseFloat(subEthBalance)))
                    ? parseFloat(subEthBalance) * ethPrice * (subEthPnlPercent / 100)
                    : null
                }
                onClick={() => setSelectedAsset('ETH')}
                isSelected={selectedAsset === 'ETH'}
              />
              <AssetCard
                icon="cryptocurrency-color:sol"
                name="Solana"
                symbol="SOL"
                balance={currentView === 'parent' ? formatBalance(solBalance) : formatBalance(subSolBalance)}
                price={solPrice}
                pnlPercent={currentView === 'parent' ? solPnlPercent : subSolPnlPercent}
                pnlValue={
                    (currentView === 'parent' && solPrice && solPnlPercent !== null && !isNaN(parseFloat(solBalance)))
                    ? parseFloat(solBalance) * solPrice * (solPnlPercent / 100)
                    : (currentView === 'subAccount' && solPrice && subSolPnlPercent !== null && !isNaN(parseFloat(subSolBalance)))
                    ? parseFloat(subSolBalance) * solPrice * (subSolPnlPercent / 100)
                    : null
                }
                onClick={() => setSelectedAsset('SOL')}
                isSelected={selectedAsset === 'SOL'}
              />
            </div>

            {/* Action Buttons Row (6 buttons) */}
            <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-8">
                {/* Send Button */}
                <Button
                    variant="web3outline"
                    onClick={() => openTransactionModal(selectedAsset)}
                    disabled={isWalletLoading || (currentView === 'parent' ? (!ethAddress && !solAddress) : (!subEthAddress && !subSolAddress))}
                    className="text-sm py-2"
                >
                    Send {selectedAsset}
                </Button>
                {/* Receive Button */}
                <Button
                    variant="web3outline"
                    onClick={() => openReceivePanel(selectedAsset, currentView === 'parent' ? (selectedAsset === 'ETH' ? ethAddress : solAddress) : (selectedAsset === 'ETH' ? subEthAddress : subSolAddress) )}
                    disabled={isWalletLoading || (currentView === 'parent' ? (!ethAddress && !solAddress) : (!subEthAddress && !subSolAddress))}
                    className="text-sm py-2"
                >
                    Receive {selectedAsset}
                </Button>
                 {/* Get Test Tokens Button */}
                <Button
                  variant="web3outline"
                    onClick={() => handleGetTestTokens(selectedAsset === 'ETH' ? 'eth' : 'sol')}
                    disabled={isWalletLoading}
                    className="text-sm py-2"
                >
                    Get Test {selectedAsset}
                </Button>
                {/* NFT Gen Button */}
                <Button
                  variant="web3outline"
                    onClick={handleOpenNFTGen}
                    disabled={isWalletLoading || !ethAddress}
                    className="text-sm py-2"
                >
                    NFT Gen
                </Button>
                {/* Test WebSocket Button */}
                <Button
                  variant="web3outline"
                    onClick={() => testNFTGenWebSocketConnection(`ws://${API_CONFIG.SERVER_HOST}:7101/ws`)}
                    disabled={isWalletLoading}
                    className="text-sm py-2"
                >
                    Test WS
                </Button>
                {/* SCGen Button - New */}
                  <Button
                    variant="web3outline"
                    onClick={() => window.open('http://***********:3002', '_blank')}
                    disabled={isWalletLoading}
                    className="text-sm py-2"
                  >
                    SCGen
                  </Button>
              </div>

            {/* Enhanced Trading Chart with Analytics Panel */}
            <div className="mb-8">
              <EnhancedTradingChart symbol={selectedAsset + 'USDT'} />
            </div>

            {/* Additional Trading Widgets */}
            <div className="bg-gray-900 bg-opacity-70 backdrop-blur-sm rounded-lg p-4 md:p-6 mb-8">
              <div className="flex flex-wrap gap-6">
                <OrderbookWidget symbol={selectedAsset + 'USDT'} />
                <TradeTapeWidget symbol={selectedAsset + 'USDT'} />
              </div>
            </div>

            {/* Activity Log Section */}
            <div className="bg-gray-900 bg-opacity-70 backdrop-blur-sm rounded-lg p-4 md:p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">Activity Log ({selectedAsset} - {currentView === 'parent' ? 'Parent' : 'Sub-Account'})</h2>
              {isActivityLoading ? (
                <div className="text-center py-4">Loading activity...</div>
              ) : activityLog.length === 0 ? (
                activityLogError ? (
                    <div className="text-center py-4 text-red-400">Failed to load activity. Please try again later.</div>
                ) : (
                    <div className="text-center py-4 text-gray-500">No activity found for this asset/view.</div>
                )
              ) : (
                <ul className="space-y-3 max-h-60 overflow-y-auto">
                  {activityLog.map((tx, index) => (
                    <li key={index} className="text-sm border-b border-gray-700 pb-2 last:border-b-0">
                      {selectedAsset === 'ETH' ? (
                        <div className="flex justify-between items-center">
                          <div>
                             <p>Type: <span className="font-medium">{tx.category} ({tx.direction})</span></p>
                             <p>Hash: <a href={EXTERNAL_URLS.ETHERSCAN_TX(tx.hash)} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline truncate w-32 inline-block">{tx.hash}</a></p>
                             <p>Value: {tx.value ?
                               (() => {
                                 try {
                                   // Handle small decimal values that aren't in hex/BigInt format
                                   if (typeof tx.value === 'string' && !tx.value.startsWith('0x') && parseFloat(tx.value) < 1) {
                                     return `${parseFloat(tx.value)} ${tx.asset}`;
                                   }
                                   // Use formatUnits for hex/BigInt values
                                   return `${ethers.formatUnits(tx.value, tx.asset === 'ETH' ? 18 : 6)} ${tx.asset}`;
                                 } catch (error) {
                                   // Fallback to directly displaying the value if formatUnits fails
                                   return `${tx.value} ${tx.asset}`;
                                 }
                               })()
                               : 'N/A'}</p>
                          </div>
                          <span className={`px-2 py-1 rounded text-xs ${
                            // Get block explorer data - prioritize the newest information
                            tx.status === 'success' || tx.metadata?.blockTimestamp ? 'bg-green-600' :
                            tx.status === 'failed' ? 'bg-red-600' : 'bg-yellow-600'
                          }`}>
                            {tx.status === 'success' ? 'Confirmed' :
                             tx.status === 'failed' ? 'Failed' :
                             tx.metadata?.blockTimestamp ? 'Confirmed' : 'Pending'}
                            {tx.metadata?.blockTimestamp ? ' (' + new Date(tx.metadata.blockTimestamp).toLocaleDateString() + ')' : ''}
                          </span>
                        </div>
                      ) : (
                        <div className="flex justify-between items-center">
                           <div>
                             <p>Signature: <a href={EXTERNAL_URLS.SOLSCAN_TX(tx.signature)} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline truncate w-32 inline-block">{tx.signature}</a></p>
                             <p>Fee: {(() => {
                                // Handle fee amount to avoid NaN
                                if (tx.fee !== undefined && tx.fee !== null) {
                                  const feeValue = Number(tx.fee);
                                  if (!isNaN(feeValue)) {
                                    return (feeValue / LAMPORTS_PER_SOL).toFixed(6);
                                  }
                                }
                                // Default value if fee is missing or NaN
                                return '0.000000';
                              })()} SOL</p>
                             {tx.amount && (
                              <p>Amount: {(() => {
                                // Handle amount to avoid NaN
                                const amountValue = Number(tx.amount);
                                if (!isNaN(amountValue)) {
                                  return (amountValue / LAMPORTS_PER_SOL).toFixed(6);
                                }
                                return '0.000000';
                              })()} SOL</p>
                             )}
                           </div>
                           <span className={`px-2 py-1 rounded text-xs ${
                             tx.status === 'confirmed' || tx.status === 'success' || tx.status === 'finalized' ? 'bg-green-600' :
                             tx.status === 'failed' ? 'bg-red-600' : 'bg-yellow-600'
                           }`}>
                            {tx.status === 'confirmed' || tx.status === 'finalized' ? 'Confirmed' :
                             tx.status === 'success' ? 'Success' :
                             tx.status === 'failed' ? 'Failed' : 'Pending'}
                              {tx.timestamp ? ` (${new Date(tx.timestamp).toLocaleDateString()})` : ''}
                            </span>
                          </div>
                        )}
                      </li>
                    ))}
                </ul>
              )}
            </div>

            {/* NFT Activities Section */}
            <div className="bg-gray-900 bg-opacity-70 backdrop-blur-sm rounded-lg p-4 md:p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">NFT Activities</h2>
                <a
                  href={EXTERNAL_URLS.NFTGEN_URL}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-3 py-1.5 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 flex items-center"
                >
                  <span className="mr-1">View NFTs</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
              <NFTActivityList />
            </div>
          </>
        )}

        {/* Modals and Panels */}
        {receivePanelVisible && (
          <ReceivePanel
            visible={receivePanelVisible}
            setVisible={setReceivePanelVisible}
            publicKey={currentAddress}
            chainType={currentChain}
          />
        )}
        {parentalControlPanelVisible && (
          <ParentalControlPanel
            onSave={handlePCSave}
            onClose={() => setParentalControlPanelVisible(false)}
          />
        )}
        {transactionModalVisible && (
          <TransactionModal
            visible={transactionModalVisible}
            setVisible={setTransactionModalVisible}
            type={currentChain}
            originatingAddress={
              currentView === 'parent'
              ? (currentChain === 'ETH' ? ethAddress : solAddress)
              : (currentChain === 'ETH' ? subEthAddress : subSolAddress)
            }
            parentalControlEnabled={isParentalControlEnabled}
            parentalSettings={parentalSettings}
            currentView={currentView}
            ethPrice={ethPrice}
            solPrice={solPrice}
            onTransactionComplete={handleTransactionComplete}
            loggedInUserEmail={loggedInEmail}
          />
        )}
      </div>

      {/* Footer */}
      <footer className="bg-gray-900/50 text-gray-400 text-center py-4">
        <p>Powered By Nija</p>
      </footer>

    </div>
  );
}