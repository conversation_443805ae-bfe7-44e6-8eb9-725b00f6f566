#!/usr/bin/env python3
"""
Generate OMNI logo for the whitepaper and overview documents
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.path import Path
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.font_manager as fm
from PIL import Image, ImageDraw, ImageFont
import os

def generate_quantum_background(size=(1000, 1000)):
    """Generate a quantum-inspired background"""
    # Create a figure with transparent background
    fig, ax = plt.subplots(figsize=(10, 10), dpi=100)
    fig.patch.set_alpha(0.0)
    ax.set_aspect('equal')
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')

    # Create custom colormap
    colors = [(0, 0, 0.4), (0, 0.2, 0.6), (0.2, 0.4, 0.8), (0.4, 0.6, 1.0)]
    cmap = LinearSegmentedColormap.from_list("OmniBlue", colors)

    # Generate quantum wave patterns
    x = np.linspace(0, 1, 1000)
    y = np.linspace(0, 1, 1000)
    X, Y = np.meshgrid(x, y)

    # Create wave patterns
    Z1 = 0.5 * np.sin(X * 50) * np.sin(Y * 50)
    Z2 = 0.3 * np.sin(X * 30 + Y * 30)
    Z3 = 0.2 * np.cos(X * 20 - Y * 20)
    Z = Z1 + Z2 + Z3

    # Plot contour
    contour = ax.contourf(X, Y, Z, 50, cmap=cmap, alpha=0.7)

    # Add some quantum particle effects
    for _ in range(20):
        x0, y0 = np.random.rand(), np.random.rand()
        size = np.random.rand() * 0.02 + 0.01
        ax.add_patch(plt.Circle((x0, y0), size, color='white', alpha=0.7))

    # Save the background
    plt.savefig('quantum_bg.png', dpi=100, bbox_inches='tight', pad_inches=0, transparent=True)
    plt.close()

    # Open and resize
    img = Image.open('quantum_bg.png')
    img = img.resize(size, Image.Resampling.LANCZOS)
    return img

def generate_infinity_symbol(size=(500, 200), color=(255, 255, 255, 200)):
    """Generate an infinity symbol"""
    # Create a transparent image
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    # Calculate dimensions
    width, height = size
    center_x, center_y = width // 2, height // 2
    radius = min(width // 4, height // 2)

    # Draw the infinity symbol
    points = []
    for t in np.linspace(0, 2*np.pi, 100):
        x = center_x + radius * np.sin(t)
        y = center_y + radius * np.sin(t) * np.cos(t)
        points.append((x, y))

    # Draw the path
    for i in range(len(points) - 1):
        draw.line([points[i], points[i+1]], fill=color, width=10)

    return img

def generate_logo():
    """Generate the OMNI logo using Nija Diia logo as base"""
    try:
        # Load the Nija Diia logo
        nija_logo_path = os.path.join('projects', 'omni', 'ui', 'dashboard', 'src', 'assets', 'images', 'nija-diia-logo.png')
        if not os.path.exists(nija_logo_path):
            print(f"Warning: Nija Diia logo not found at {nija_logo_path}")
            return generate_fallback_logo()

        # Create base image
        size = (800, 800)
        logo = Image.new('RGBA', size, (0, 0, 0, 0))

        # Load and resize Nija Diia logo
        nija_logo = Image.open(nija_logo_path).convert('RGBA')
        nija_logo = nija_logo.resize((400, 400), Image.Resampling.LANCZOS)

        # Paste Nija Diia logo in the center
        logo.paste(nija_logo, (200, 200), nija_logo)

        # Add OMNI text
        draw = ImageDraw.Draw(logo)

        # Try to find a suitable font
        font_paths = [
            '/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf',  # Linux
            '/Library/Fonts/Arial Bold.ttf',  # macOS
            'C:\\Windows\\Fonts\\arialbd.ttf',  # Windows
            '/usr/share/fonts/TTF/DejaVuSans-Bold.ttf',  # Arch Linux
        ]

        font_path = None
        for path in font_paths:
            if os.path.exists(path):
                font_path = path
                break

        if font_path:
            try:
                font_large = ImageFont.truetype(font_path, 100)
                font_small = ImageFont.truetype(font_path, 40)
            except IOError:
                font_large = ImageFont.load_default()
                font_small = ImageFont.load_default()
        else:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()

        # Draw text
        draw.text((400, 100), "OMNI", fill=(255, 255, 255, 255), font=font_large, anchor="mm")
        draw.text((400, 650), "ALPHA VΩ∞∞", fill=(255, 255, 255, 255), font=font_small, anchor="mm")

        # Save the logo
        logo.save('omni_logo.png')
        print("Logo generated: omni_logo.png")
        return logo

    except Exception as e:
        print(f"Error generating logo: {e}")
        return generate_fallback_logo()

def generate_fallback_logo():
    """Generate a fallback logo if Nija Diia logo is not available"""
    # Create base image
    size = (800, 800)
    logo = Image.new('RGBA', size, (0, 0, 0, 0))

    # Generate quantum background
    bg = generate_quantum_background(size)
    logo.paste(bg, (0, 0), bg)

    # Create circular mask
    mask = Image.new('L', size, 0)
    draw = ImageDraw.Draw(mask)
    draw.ellipse((50, 50, size[0]-50, size[1]-50), fill=255)

    # Apply mask to create circular logo
    logo.putalpha(mask)

    # Add infinity symbol
    infinity = generate_infinity_symbol((400, 200), color=(255, 255, 255, 200))
    logo.paste(infinity, (200, 300), infinity)

    # Add text
    draw = ImageDraw.Draw(logo)
    font_large = ImageFont.load_default()
    font_small = ImageFont.load_default()

    # Draw text
    draw.text((400, 200), "OMNI", fill=(255, 255, 255, 255), font=font_large, anchor="mm")
    draw.text((400, 500), "ALPHA VΩ∞∞", fill=(255, 255, 255, 255), font=font_small, anchor="mm")

    # Save the logo
    logo.save('omni_logo.png')
    print("Fallback logo generated: omni_logo.png")
    return logo

def generate_concept_image():
    """Generate a concept visualization for the overview document"""
    # Create base image
    size = (1000, 600)
    img = Image.new('RGBA', size, (255, 255, 255, 255))
    draw = ImageDraw.Draw(img)

    # Draw background gradient
    for y in range(size[1]):
        # Create a blue gradient
        r = int(0 + (y / size[1]) * 50)
        g = int(50 + (y / size[1]) * 100)
        b = int(100 + (y / size[1]) * 155)
        draw.line([(0, y), (size[0], y)], fill=(r, g, b))

    # Draw network nodes
    nodes = [
        (200, 200, "Quantum\nPredictor", (102, 0, 204)),
        (400, 100, "Ghost\nTrader", (0, 51, 102)),
        (600, 200, "Memory\nNode", (51, 153, 255)),
        (300, 350, "Macro\nSentinel", (204, 153, 0)),
        (500, 350, "Zero Loss\nEnforcer", (0, 153, 51)),
        (800, 300, "Exchange\nAPI", (204, 0, 0))
    ]

    # Draw connections
    for i, (x1, y1, _, _) in enumerate(nodes):
        for j, (x2, y2, _, _) in enumerate(nodes):
            if i < j:  # Avoid duplicate connections
                # Calculate distance
                dist = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                if dist < 300:  # Only connect nearby nodes
                    # Draw line with gradient
                    for t in np.linspace(0, 1, 50):
                        x = int(x1 + t * (x2 - x1))
                        y = int(y1 + t * (y2 - y1))
                        size = int(3 * (1 - abs(2*t - 1)))  # Thicker in the middle
                        alpha = int(100 + 155 * (1 - abs(2*t - 1)))  # More opaque in the middle
                        color = (0, 100, 200, alpha)
                        draw.ellipse((x-size, y-size, x+size, y+size), fill=color)

    # Draw nodes
    for x, y, label, color in nodes:
        # Draw glow
        for size in range(40, 30, -2):
            alpha = int(10 + (40 - size) * 5)
            glow_color = color + (alpha,)
            draw.ellipse((x-size, y-size, x+size, y+size), fill=glow_color)

        # Draw node
        draw.ellipse((x-30, y-30, x+30, y+30), fill=color)

        # Draw label
        font_path = None
        for path in [
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
            '/Library/Fonts/Arial.ttf',
            'C:\\Windows\\Fonts\\arial.ttf',
            '/usr/share/fonts/TTF/DejaVuSans.ttf',
        ]:
            if os.path.exists(path):
                font_path = path
                break

        if font_path:
            try:
                font = ImageFont.truetype(font_path, 16)
            except IOError:
                font = ImageFont.load_default()
        else:
            font = ImageFont.load_default()

        # Split label by newline and draw each line
        lines = label.split('\n')
        for i, line in enumerate(lines):
            text_width = draw.textlength(line, font=font)
            draw.text((x - text_width/2, y + 40 + i*20), line, fill=(0, 0, 0), font=font)

    # Add title
    if font_path:
        try:
            title_font = ImageFont.truetype(font_path, 30)
        except IOError:
            title_font = ImageFont.load_default()
    else:
        title_font = ImageFont.load_default()

    title = "OMNI Multi-Agent Trading System"
    title_width = draw.textlength(title, font=title_font)
    draw.text((size[0]/2 - title_width/2, 30), title, fill=(0, 0, 0), font=title_font)

    # Save the image
    img.save('omni_concept.png')
    print("Concept image generated: omni_concept.png")

def generate_quantum_prediction_image():
    """Generate a quantum prediction visualization"""
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 6), dpi=100)

    # Set up the plot
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.set_xlabel('Time', fontsize=12)
    ax.set_ylabel('Price', fontsize=12)
    ax.set_title('Quantum-Inspired Price Prediction', fontsize=16)
    ax.grid(True, alpha=0.3)

    # Generate historical price data
    x_hist = np.arange(0, 50)
    y_hist = 50 + 10 * np.sin(x_hist / 10) + np.random.normal(0, 2, len(x_hist))

    # Plot historical data
    ax.plot(x_hist, y_hist, 'b-', linewidth=2, label='Historical Price')

    # Generate quantum prediction paths
    x_pred = np.arange(50, 100)

    # Main prediction path
    y_pred_main = y_hist[-1] + 5 * np.sin((x_pred - 50) / 10) + (x_pred - 50) * 0.1

    # Alternative paths with different probabilities
    paths = []
    probabilities = [0.6, 0.3, 0.1]  # Probabilities for each path

    paths.append(y_pred_main)
    paths.append(y_hist[-1] + 3 * np.sin((x_pred - 50) / 8) - (x_pred - 50) * 0.05)
    paths.append(y_hist[-1] + 7 * np.sin((x_pred - 50) / 12) + (x_pred - 50) * 0.2)

    # Plot prediction paths with transparency based on probability
    colors = ['g', 'orange', 'r']
    for i, (path, prob, color) in enumerate(zip(paths, probabilities, colors)):
        # Plot the path
        ax.plot(x_pred, path, color=color, linewidth=2, alpha=0.7,
                label=f'Prediction Path {i+1} ({prob*100:.0f}%)')

        # Add shaded area for uncertainty
        uncertainty = 2 + (1 - prob) * 10  # More uncertainty for less probable paths
        ax.fill_between(x_pred, path - uncertainty, path + uncertainty,
                        color=color, alpha=0.2)

    # Add quantum effects
    for i in range(20):
        x = np.random.uniform(50, 100)
        y = np.random.uniform(30, 70)
        size = np.random.uniform(20, 100)
        alpha = np.random.uniform(0.05, 0.2)
        ax.add_patch(plt.Circle((x, y), size, color='blue', alpha=alpha))

    # Add vertical line separating history from prediction
    ax.axvline(x=50, color='k', linestyle='--', alpha=0.5, label='Present')

    # Add entry and exit points
    ax.plot(60, paths[0][10], 'go', markersize=10, label='Entry Point')
    ax.plot(80, paths[0][30], 'ro', markersize=10, label='Exit Point')

    # Add legend
    ax.legend(loc='upper left', fontsize=10)

    # Add annotations
    ax.annotate('Quantum Superposition\nof Possible Futures',
                xy=(75, 65), xytext=(60, 80),
                arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8, alpha=0.7))

    # Save the image
    plt.tight_layout()
    plt.savefig('quantum_prediction.png', dpi=100, bbox_inches='tight')
    plt.close()
    print("Quantum prediction image generated: quantum_prediction.png")

def generate_self_evolution_image():
    """Generate a self-evolution process visualization"""
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 6), dpi=100)

    # Set up the plot
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.axis('off')

    # Create a circular flow diagram
    center_x, center_y = 50, 50
    radius = 30

    # Define the stages
    stages = [
        (0, "Market Analysis", (0.2, 0.4, 0.8)),
        (1, "Pattern Recognition", (0.4, 0.2, 0.8)),
        (2, "Trade Execution", (0.8, 0.2, 0.4)),
        (3, "Performance Evaluation", (0.8, 0.4, 0.2)),
        (4, "Memory Formation", (0.2, 0.8, 0.4)),
        (5, "Strategy Refinement", (0.4, 0.8, 0.2))
    ]

    # Draw the circular flow
    for i, (idx, label, color) in enumerate(stages):
        # Calculate position
        angle = idx * 2 * np.pi / len(stages)
        x = center_x + radius * np.cos(angle)
        y = center_y + radius * np.sin(angle)

        # Draw node
        circle = plt.Circle((x, y), 8, color=color, alpha=0.8)
        ax.add_patch(circle)

        # Draw label
        ax.annotate(label, xy=(x, y), xytext=(x + 15 * np.cos(angle), y + 15 * np.sin(angle)),
                   ha='center', va='center', fontsize=10,
                   bbox=dict(boxstyle="round,pad=0.3", fc="white", ec=color, alpha=0.8))

        # Draw arrow to next stage
        next_idx = (idx + 1) % len(stages)
        next_angle = next_idx * 2 * np.pi / len(stages)
        next_x = center_x + radius * np.cos(next_angle)
        next_y = center_y + radius * np.sin(next_angle)

        # Calculate control points for curved arrow
        mid_angle = (angle + next_angle) / 2
        ctrl_x = center_x + 1.3 * radius * np.cos(mid_angle)
        ctrl_y = center_y + 1.3 * radius * np.sin(mid_angle)

        # Create a curved path
        verts = [
            (x, y),  # start
            (ctrl_x, ctrl_y),  # control point
            (next_x, next_y)  # end
        ]
        codes = [Path.MOVETO, Path.CURVE3, Path.CURVE3]
        path = Path(verts, codes)
        patch = patches.PathPatch(path, facecolor='none', edgecolor=color, lw=2, alpha=0.8)
        ax.add_patch(patch)

    # Add central "Self-Evolution" text
    central_circle = plt.Circle((center_x, center_y), 15, color=(0.1, 0.2, 0.5), alpha=0.8)
    ax.add_patch(central_circle)
    ax.text(center_x, center_y, "Self\nEvolution", ha='center', va='center', color='white', fontsize=10)

    # Add title
    ax.text(center_x, 10, "OMNI Self-Evolution Process", ha='center', va='center', fontsize=16,
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec=(0.1, 0.2, 0.5), alpha=0.8))

    # Add explanation text
    explanation = (
        "The OMNI system continuously evolves through a feedback loop:\n"
        "1. Analyzing market conditions\n"
        "2. Recognizing patterns based on past experience\n"
        "3. Executing trades with optimal parameters\n"
        "4. Evaluating performance against expectations\n"
        "5. Forming memories of successful and unsuccessful trades\n"
        "6. Refining strategies based on accumulated knowledge"
    )
    ax.text(center_x, 85, explanation, ha='center', va='center', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec=(0.1, 0.2, 0.5), alpha=0.9))

    # Save the image
    plt.tight_layout()
    plt.savefig('self_evolution.png', dpi=100, bbox_inches='tight')
    plt.close()
    print("Self-evolution image generated: self_evolution.png")

def generate_dashboard_mockup():
    """Generate a dashboard mockup with proper error handling and dynamic data"""
    try:
        # Create base image
        width, height = 1000, 600
        image = Image.new('RGB', (width, height), (255, 255, 255))
        draw = ImageDraw.Draw(image)

        # Load fonts with error handling
        try:
            title_font = ImageFont.truetype("arial.ttf", 24)
            label_font = ImageFont.truetype("arial.ttf", 16)
            small_font = ImageFont.truetype("arial.ttf", 12)
        except IOError:
            # Fallback to default font if custom fonts not available
            title_font = ImageFont.load_default()
            label_font = ImageFont.load_default()
            small_font = ImageFont.load_default()

        # Fetch real portfolio data
        try:
            portfolio_data = fetch_portfolio_data()  # Implement this function to get real data
        except Exception as e:
            print(f"Error fetching portfolio data: {e}")
            portfolio_data = {
                'initial_capital': 0,
                'current_value': 0,
                'profit_loss': 0
            }

        # Fetch real trade data
        try:
            active_trades = fetch_active_trades()  # Implement this function to get real data
        except Exception as e:
            print(f"Error fetching active trades: {e}")
            active_trades = []

        # Draw portfolio summary with real data
        draw.text((230, 90), "Portfolio Summary", fill=(0, 0, 0), font=title_font)
        draw.text((230, 120), "Initial Capital:", fill=(0, 0, 0), font=label_font)
        draw.text((350, 120), f"${portfolio_data['initial_capital']:.2f}", fill=(0, 0, 0), font=label_font)
        draw.text((230, 145), "Current Value:", fill=(0, 0, 0), font=label_font)
        draw.text((350, 145), f"${portfolio_data['current_value']:.2f}", 
                 fill=(0, 102, 0) if portfolio_data['current_value'] > portfolio_data['initial_capital'] else (204, 0, 0), 
                 font=label_font)
        draw.text((230, 170), "Profit/Loss:", fill=(0, 0, 0), font=label_font)
        draw.text((350, 170), f"{portfolio_data['profit_loss']:+.1f}%", 
                 fill=(0, 102, 0) if portfolio_data['profit_loss'] > 0 else (204, 0, 0), 
                 font=label_font)

        # Draw active trades with real data
        draw.rectangle([(500, 80), (980, 200)], fill=(255, 255, 255), outline=(200, 200, 200))
        draw.text((510, 90), "Active Trades", fill=(0, 0, 0), font=title_font)

        # Table headers
        headers = ["Symbol", "Side", "Entry", "Current", "P/L", "Status"]
        header_widths = [80, 60, 80, 80, 60, 100]
        x_pos = 510
        for header, width in zip(headers, header_widths):
            draw.text((x_pos, 120), header, fill=(100, 100, 100), font=small_font)
            x_pos += width

        # Table rows with real trade data
        for i, trade in enumerate(active_trades):
            y_pos = 145 + i * 25
            x_pos = 510
            for j, (value, width) in enumerate(zip(trade, header_widths)):
                color = (0, 0, 0)
                if j == 1:  # Side column
                    color = (0, 102, 0) if value == "BUY" else (204, 0, 0)
                elif j == 4:  # P/L column
                    color = (0, 102, 0) if "+" in str(value) else (204, 0, 0)
                draw.text((x_pos, y_pos), str(value), fill=color, font=small_font)
                x_pos += width

        # Draw trading performance chart
        draw.rectangle([(220, 220), (980, 400)], fill=(255, 255, 255), outline=(200, 200, 200))
        draw.text((230, 230), "Trading Performance", fill=(0, 0, 0), font=title_font)

        # Draw chart with real performance data
        try:
            performance_data = fetch_performance_data()  # Implement this function to get real data
            draw_performance_chart(draw, performance_data, 240, 380)  # Implement this function to draw the chart
        except Exception as e:
            print(f"Error drawing performance chart: {e}")
            draw.text((240, 300), "Performance data unavailable", fill=(100, 100, 100), font=label_font)

        return image

    except Exception as e:
        print(f"Error generating dashboard mockup: {e}")
        # Return a simple error image
        error_image = Image.new('RGB', (width, height), (255, 255, 255))
        error_draw = ImageDraw.Draw(error_image)
        error_draw.text((width//2, height//2), "Error generating dashboard", fill=(204, 0, 0), font=title_font)
        return error_image

if __name__ == "__main__":
    generate_logo()
    generate_concept_image()
    generate_quantum_prediction_image()
    generate_self_evolution_image()
    generate_dashboard_mockup()
